<template>
  <q-page class="card-detail-mobile">
    <!-- Fixed Header Image -->
    <div class="header-image-fixed">
      <q-img
        :src="item.photoUrl ? item.photoUrl : '/images/image_default.jpg'"
        fit="cover"
        class="full-width full-height"
      />
      <div class="absolute-full bg-black" style="opacity: 0.2"></div>
      <div
        class="absolute-top q-mt-md q-px-md q-py-xs row items-center justify-between"
        style="z-index: 10"
      >
        <q-btn
          round
          color="white"
          text-color="black"
          dense
          icon="arrow_back"
          to="/events"
          class="header-btn"
        />
        <div class="header-actions row q-gutter-sm">
          <q-btn
            round
            color="white"
            text-color="black"
            dense
            @click="handleShareEvent"
            class="header-btn"
          >
            <Share :size="16" />
          </q-btn>
        </div>
      </div>
      <div class="absolute-bottom-left q-ma-sm">
        <q-chip text-color="white" color="primary">
          {{ formatPrice(item.price) }} - {{ formatPrice(item.price) }}
        </q-chip>
      </div>
    </div>

    <!-- Scrollable Content Area -->
    <div class="content-scrollable">
      <div class="card-inner">
        <div v-if="$slots.tags" class="row items-center q-gutter-x-sm">
          <slot name="tags"></slot>
        </div>
        <div class="row items-center justify-between full-width no-wrap q-mb-sm">
          <div class="text-bold cursor-pointer text-h5 q-mr-md" style="flex: 1">
            {{ item.title }}
          </div>
          <div v-if="$slots.rating" class="rating-section">
            <slot name="rating"></slot>
          </div>
        </div>
        <!-- Description -->
        <div v-if="$slots.description" class="description-section">
          <slot name="description"></slot>
        </div>
        <div class="organizer-section">
          <slot name="organizer"></slot>
        </div>
        <!-- Enhanced Event Details -->
        <div class="event-details q-mb-md">
          <slot name="eventDetails"></slot>
        </div>
        <div v-if="$slots.activities" class="activities-section">
          <slot name="activities"></slot>
        </div>
        <div v-if="$slots.participants" class="participants-section q-mt-md">
          <slot name="participants"></slot>
        </div>
        <div v-if="$slots.amenities" class="amenities-section">
          <slot name="amenities"></slot>
        </div>
      </div>
    </div>
    <!-- Footer -->
    <q-page-sticky
      position="bottom"
      style="border-top: none"
      expand
      class="bottom-toolbar bg-white justify-between q-pa-md"
    >
      <slot name="footerActions"></slot>
    </q-page-sticky>
  </q-page>
  <ShareModal :is-open="isShareModalOpen" :url="shareUrl" :onClose="closeShareModal" />
</template>
<script setup lang="ts">
import { Share } from 'lucide-vue-next';
import ShareModal from 'src/components/common/ShareModal/ShareModal.vue';
import { computed, ref } from 'vue';

interface DetailCardProps {
  item: {
    id?: string | number;
    title: string;
    rating: number;
    description: string;
    location: string;
    date: string;
    startTime: string;
    endTime: string;
    photoUrl: string;
    skillLevels: string[];
    price: number;
    [key: string]: any;
  };
}

const props = defineProps<DetailCardProps>();
const item = computed(() => props.item);
const isShareModalOpen = ref(false);

const shareUrl = computed(() => {
  const baseUrl = window.location.origin;
  const slug = encodeURIComponent(item.value.title.trim().replace(/\s+/g, '-').toLowerCase());
  const id = item.value.id || slug;
  return `${baseUrl}/events/${slug}?eventId=${id}`;
});

const handleShareEvent = (e?: Event) => {
  e?.stopPropagation();
  isShareModalOpen.value = true;
};

const closeShareModal = () => {
  isShareModalOpen.value = false;
};

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(price);
};
</script>

<style scoped lang="scss">
// Mobile-First Layout with Fixed Header & Footer
.card-detail-mobile {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
  overflow: hidden;
}

// Fixed Header Image
.header-image-fixed {
  position: relative;
  height: 18rem;
  flex-shrink: 0;
  z-index: 2;
}

// Scrollable Content Area
.content-scrollable {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  background: white;

  // Hide scrollbar for cleaner look
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;
}

// Card Inner Content
.card-inner {
  position: relative;
  z-index: 1;
  background-color: white;
  padding: 20px;
  min-height: calc(100vh - 18rem - 80px); // Ensure content fills available space
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 80px;
}

// Header Action Buttons
.header-btn {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 1) !important;
    transform: scale(1.05);
  }
}

.header-actions {
  .header-btn {
    transition: all 0.2s ease;
  }
}

// Fixed Footer Toolbar
.bottom-toolbar {
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 690px !important;
  margin: 0 auto;
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95) !important;
  z-index: 1000;
}

// Mobile Responsive Optimizations
@media (max-width: 768px) {
  .header-image-fixed {
    height: 16rem;
  }

  .card-inner {
    border-radius: 20px 20px 0 0;
    padding: 16px;
    min-height: calc(100vh - 16rem - 80px);
    margin-bottom: 80px;
  }

  .content-scrollable {
    // Better mobile scrolling
    overscroll-behavior-y: contain;
  }
}

@media (max-width: 480px) {
  .header-image-fixed {
    height: 14rem;
  }

  .card-inner {
    border-radius: 16px 16px 0 0;
    padding: 12px;
    min-height: calc(100vh - 14rem - 70px);
    margin-bottom: 70px;
  }

  .header-actions {
    gap: 6px;
  }

  .header-btn {
    width: 36px;
    height: 36px;
  }
}
</style>
