import { defineStore } from 'pinia';
import { ActivitiesService } from 'src/services/activitiesService';
import type {
  IActivity,
  IActivityQueryParams,
  ICreateActivity,
  IPaginationResponse,
  ISkillLevel,
  ISport,
  ITag,
} from 'src/types/activities';
import { ref } from 'vue';

export const useActivitiesStore = defineStore('activities', () => {
  const activities = ref<IActivity[]>([]);
  const sports = ref<ISport[]>([]);
  const skillLevels = ref<ISkillLevel[]>([]);
  const tags = ref<ITag[]>([]);
  const pagination = ref<IPaginationResponse | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const getActivities = async (params: IActivityQueryParams) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.getActivities(params);
      activities.value = response.data.activities;
      pagination.value = response.data.pagination;
      return response.data.activities;
    } catch (err) {
      console.error(err, 'Failed to get activities');
      error.value = 'Failed to get activities';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const getActivity = async (id: string) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.getActivity(id);
      return response.data.activity;
    } catch (err) {
      console.error(err, 'Failed to get activity');
      error.value = 'Failed to get activity';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const createActivity = async (data: ICreateActivity) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.createActivity(data);
      return response.data.activity;
    } catch (err) {
      console.error(err, 'Failed to create activity');
      error.value = 'Failed to create activity';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const uploadActivityPhoto = async (id: string, file: File) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.uploadActivityPhoto(id, file);
      return response.data.photoUrl;
    } catch (err) {
      console.error(err, 'Failed to upload activity photo');
      error.value = 'Failed to upload activity photo';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const getSports = async () => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.getSports();
      sports.value = response.data.sports;
      return response.data.sports;
    } catch (err) {
      console.error(err, 'Failed to get sports');
      error.value = 'Failed to get sports';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const getSkillLevels = async (sportId: string) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.getSkillLevels(sportId);
      skillLevels.value = response.data.skillLevels;
      return response.data.skillLevels;
    } catch (err) {
      console.error(err, 'Failed to get skill levels');
      error.value = 'Failed to get skill levels';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const getTags = async () => {
    loading.value = true;
    error.value = null;
    try {
      const response = await ActivitiesService.getTags();
      tags.value = response.data.tags;
      return response.data.tags;
    } catch (err) {
      console.error(err, 'Failed to get tags');
      error.value = 'Failed to get tags';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    activities,
    sports,
    skillLevels,
    tags,
    pagination,
    loading,
    error,

    // Methods
    getActivities,
    getActivity,
    createActivity,
    uploadActivityPhoto,
    getSports,
    getSkillLevels,
    getTags,
  };
});
