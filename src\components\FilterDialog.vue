<template>
  <q-card style="width: 100%">
    <div class="sticky-header">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Bộ lọc</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup @click="emit('close')" />
      </q-card-section>
      <q-separator />
    </div>
    <q-card-section class="q-pa-none">
      <q-item class="column">
        <div class="text-body1 q-mb-sm text-bold">M<PERSON>n thể thao</div>

        <!-- Simple Radio-Style Sport Selection -->
        <div class="sport-selection-radio">
          <div class="sport-options-grid">
            <div
              v-for="sport in sports"
              :key="sport.value"
              class="sport-option-item"
              :class="{ 'sport-option-item--selected': selectedSport === sport.value }"
              @click="selectedSport = sport.value"
            >
              <div class="sport-radio">
                <div class="sport-radio__circle">
                  <div v-if="selectedSport === sport.value" class="sport-radio__dot"></div>
                </div>
              </div>
              <div class="sport-label">{{ sport.label }}</div>
            </div>
          </div>
        </div>
      </q-item>

      <q-item class="column">
        <div class="text-body1 q-my-sm text-bold">Chọn ngày</div>

        <!-- Date Selection Options -->
        <div class="date-selection-container">
          <!-- Quick Date Options -->
          <div class="quick-date-options q-mb-md">
            <q-btn-toggle
              v-model="selectedDateType"
              :options="dateTypeOptions"
              toggle-color="primary"
              flat
              class="date-toggle-enhanced"
              @update:model-value="onDateTypeChange"
            />
          </div>

          <!-- Custom Date Picker (shown when 'custom' is selected) -->
          <div v-if="selectedDateType === 'custom'" class="custom-date-section">
            <div class="text-body2 q-mb-sm text-grey-7">Chọn ngày</div>
            <q-input
              v-model="customDateDisplay"
              outlined
              dense
              readonly
              class="custom-date-input"
              placeholder="dd/mm/yyyy"
              @click="showDatePicker = true"
            >
              <template v-slot:prepend>
                <q-icon name="event" color="primary" />
              </template>
              <template v-slot:append>
                <q-icon name="keyboard_arrow_down" />
              </template>
            </q-input>

            <!-- Date Picker Dialog -->
            <q-dialog v-model="showDatePicker" position="bottom">
              <q-card class="date-picker-card">
                <q-card-section class="q-pa-none">
                  <q-date
                    v-model="customDate"
                    :options="dateOptions"
                    color="primary"
                    today-btn
                    flat
                    class="full-width"
                    @update:model-value="onCustomDateSelect"
                  />
                </q-card-section>
                <q-card-actions align="right" class="q-pa-md">
                  <q-btn flat label="Cancel" @click="showDatePicker = false" />
                  <q-btn
                    color="primary"
                    label="Select"
                    @click="confirmCustomDate"
                    :disable="!customDate"
                  />
                </q-card-actions>
              </q-card>
            </q-dialog>
          </div>

          <!-- Selected Date Display -->
          <div v-if="selectedDateType !== 'custom'" class="selected-date-display q-mt-sm">
            <div class="text-caption text-grey-6">
              Selected: {{ getSelectedDateText() }}
            </div>
          </div>
        </div>
      </q-item>

      <q-item class="column">
        <div class="text-body1 q-my-sm text-bold">Trình độ</div>

        <!-- Multi-Select Skill Level Selection -->
        <div class="skill-level-multi">
          <div class="skill-chips-row">
            <q-chip
              v-for="level in skillLevels"
              :key="level.value"
              outlined
              :text-color="selectedSkillLevels.includes(level.value) ? 'primary' : 'dark'"
              clickable
              @click="toggleSkillLevel(level.value)"
            >
              <q-icon
                v-if="selectedSkillLevels.includes(level.value)"
                name="check_circle"
                size="14px"
                :color="selectedSkillLevels.includes(level.value) ? 'primary' : 'grey-3'"
                class="q-mr-xs"
              />
              {{ level.label }}
            </q-chip>
          </div>
        </div>
      </q-item>
      <q-item class="column">
        <div class="text-body1 q-mt-sm text-bold flex items-center">
          Khoảng cách
          <q-icon name="info" size="16px" class="q-ml-sm cursor-pointer">
            <q-tooltip anchor="top middle" self="center middle">
              Bán kính khoảng cách tìm kiếm từ vị trí hiện tại của bạn. 
            </q-tooltip>
          </q-icon>
        </div>

        <!-- Simple Distance Selection -->
        <div class="distance-simple">
          <!-- Current Range Display -->
          <div class="distance-current text-center q-mb-md">
            <span class="text-primary text-bold">
              {{ distanceRange.min }} - {{ distanceRange.max }} km
            </span>
          </div>

          <!-- Clean Range Slider -->
          <div class="distance-slider">
            <q-range
              v-model="distanceRange"
              :min="0"
              :max="20"
              :step="1"
              color="primary"
              class="simple-range"
            />
          </div>

          <!-- Simple Range Labels -->
          <div class="distance-labels flex justify-between">
            <span class="label-start">0 km</span>
            <span class="label-end">25 km</span>
          </div>
        </div>
      </q-item>
      <q-item class="column">
        <div class="text-body1 q-my-sm text-bold">Vị trí</div>

        <!-- Compact Location Row -->
        <div class="location-compact-row">
          <q-select
            v-model="location"
            outlined
            use-input
            hide-selected
            fill-input
            input-debounce="300"
            :options="filteredCities"
            @filter="filterLocations"
            placeholder="Chọn hoặc tìm vị trí..."
          >
            <template v-slot:prepend>
              <q-icon name="place" color="primary" size="18px" />
            </template>
            <template v-slot:append>
              <q-btn
                round
                dense
                flat
                icon="my_location"
                color="primary"
                size="sm"
                @click="useCurrentLocation"
                class="current-location-btn"
              >
                <q-tooltip class="bg-grey-8 text-body2">
                  Vị trí hiện tại
                </q-tooltip>
              </q-btn>
            </template>
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  Không tìm thấy vị trí
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
      </q-item>
    </q-card-section>

    <div class="sticky-footer">
      <q-separator />
      <q-card-actions class="q-pa-md">
        <div class="column col-12 row-sm q-gutter-sm">
          <q-btn
            outline
            color="primary"
            label="Đặt lại"
            no-caps
            :size="$q.screen.gt.sm ? 'lg' : 'md'"
            @click="resetFilters"
          />
          <q-btn
            unelevated
            color="primary"
            label="Áp dụng"
            :size="$q.screen.gt.sm ? 'lg' : 'md'"
            no-caps
            @click="applyFilters"
          />
        </div>
      </q-card-actions>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { date, useQuasar } from 'quasar';

interface Emits {
  (e: 'apply', filters: any): void;
  (e: 'close'): void;
}
const emit = defineEmits<Emits>();
const $q = useQuasar();

const locationSearch = ref('');
const selectedSport = ref('badminton');
const selectedSkillLevels = ref(['all']); // Changed to array for multi-selection
const distanceRange = ref({ min: 1, max: 5 });
const location = ref('New York, USA');

// Enhanced Date Selection
const selectedDateType = ref('today');
const customDate = ref('');
const showDatePicker = ref(false);

// Date type options for the toggle
const dateTypeOptions = ref([
  { label: 'Hôm nay', value: 'today' },
  { label: 'Ngày mai', value: 'tomorrow' },
  { label: 'Chọn ngày khác', value: 'custom' }
]);

// Get today and tomorrow dates
const today = new Date();
const tomorrow = new Date(today);
tomorrow.setDate(tomorrow.getDate() + 1);

// Custom date display
const customDateDisplay = computed(() => {
  if (!customDate.value) return '';
  return date.formatDate(customDate.value, 'dddd, MMMM D, YYYY');
});

// Date options for the date picker (disable past dates)
const dateOptions = (dateStr: string) => {
  const selectedDate = new Date(dateStr);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return selectedDate >= today;
};

const cities = ref([
  'New York, USA',
  'Miami, USA',
  'Boston, USA',
  'Seattle, USA',
  'Denver, USA',
  'Los Angeles, USA',
  'Chicago, USA',
  'Houston, USA',
  'Phoenix, USA',
  'Philadelphia, USA',
  'San Antonio, USA',
  'San Diego, USA',
  'Dallas, USA',
  'San Jose, USA',
  'Austin, USA',
  'Jacksonville, USA',
  'Fort Worth, USA',
  'Columbus, USA',
  'Charlotte, USA',
  'San Francisco, USA',
]);

const sports = ref([
  { label: 'Basketball', value: 'basketball' },
  { label: 'Badminton', value: 'badminton' },
  { label: 'Tennis', value: 'tennis' },
  { label: 'Soccer', value: 'soccer' },
  { label: 'Volleyball', value: 'volleyball' },
  { label: 'Swimming', value: 'swimming' },
  { label: 'Cricket', value: 'cricket' },
  { label: 'Table Tennis', value: 'table_tennis' },
]);

const skillLevels = ref([
  { label: 'All Levels', value: 'all' },
  { label: 'Beginner', value: 'beginner' },
  { label: 'Intermediate', value: 'intermediate' },
  { label: 'Advanced', value: 'advanced' },
  { label: 'Expert', value: 'expert' },
]);



// Date selection methods
const onDateTypeChange = (value: string) => {
  if (value !== 'custom') {
    customDate.value = '';
  }
};

const onCustomDateSelect = (value: string) => {
  customDate.value = value;
};

const confirmCustomDate = () => {
  showDatePicker.value = false;
};

const getSelectedDateText = () => {
  switch (selectedDateType.value) {
    case 'today':
      return date.formatDate(today, 'dddd, MMMM D, YYYY');
    case 'tomorrow':
      return date.formatDate(tomorrow, 'dddd, MMMM D, YYYY');
    default:
      return '';
  }
};

const getSelectedDateValue = () => {
  switch (selectedDateType.value) {
    case 'today':
      return date.formatDate(today, 'YYYY-MM-DD');
    case 'tomorrow':
      return date.formatDate(tomorrow, 'YYYY-MM-DD');
    case 'custom':
      return customDate.value;
    default:
      return '';
  }
};

// Multi-select skill level functionality
const toggleSkillLevel = (value: string) => {
  if (value === 'all') {
    // If "All Levels" is selected, clear other selections
    selectedSkillLevels.value = ['all'];
  } else {
    // Remove "All Levels" if another option is selected
    const allIndex = selectedSkillLevels.value.indexOf('all');
    if (allIndex > -1) {
      selectedSkillLevels.value.splice(allIndex, 1);
    }

    // Toggle the selected skill level
    const index = selectedSkillLevels.value.indexOf(value);
    if (index > -1) {
      selectedSkillLevels.value.splice(index, 1);
    } else {
      selectedSkillLevels.value.push(value);
    }

    // If no specific levels are selected, default to "All Levels"
    if (selectedSkillLevels.value.length === 0) {
      selectedSkillLevels.value = ['all'];
    }
  }
};



const useCurrentLocation = () => {
  // Implementation for using current location
  console.log('Using current location');
};

const filteredCities = computed(() => {
  if (!locationSearch.value) return cities.value;
  return cities.value.filter((c) => c.toLowerCase().includes(locationSearch.value.toLowerCase()));
});

// New filter function for the select component
const filterLocations = (val: string, update: (fn: () => void) => void) => {
  update(() => {
    locationSearch.value = val;
  });
};

const resetFilters = () => {
  // Reset all filters to default values
  selectedSport.value = 'badminton';
  selectedSkillLevels.value = ['all'];
  selectedDateType.value = 'today';
  customDate.value = '';
  distanceRange.value = { min: 0, max: 15 };
  location.value = 'New York, USA';
  locationSearch.value = '';

  // Show confirmation feedback
  // Note: You can add a toast notification here if needed
  console.log('Filters reset to default values');
};

const applyFilters = () => {
  const filters = {
    sport: selectedSport.value,
    skillLevels: selectedSkillLevels.value, // Changed to array
    dateType: selectedDateType.value,
    date: getSelectedDateValue(),
    dateDisplay: selectedDateType.value === 'custom' ? customDateDisplay.value : getSelectedDateText(),
    distance: {
      min: distanceRange.value.min,
      max: distanceRange.value.max,
    },
    location: location.value,
  };

  emit('apply', filters);
};
</script>

<style lang="scss" scoped>
// Sticky Header & Footer with Separators
.sticky-header {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;

  // Ensure separator is included in sticky area
  .q-separator {
    margin: 16px 0 8px 0;
  }
}

.sticky-footer {
  position: sticky;
  bottom: 0;
  background-color: white;
  z-index: 10;
}

.q-chip {
  &.bg-primary .q-chip__icon {
    .q-chip__icon {
      background-color: white !important;
    }
  }
}

// Radio-Style Sport Selection - 3 Items Per Row
.sport-selection-radio {
  .sport-options-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;

    .sport-option-item {
      display: flex;
      align-items: center;
      padding: 10px 8px;
      border: 1.5px solid #e9ecef;
      border-radius: 8px;
      background-color: #ffffff;
      cursor: pointer;
      transition: all 0.2s ease;
      min-height: 44px;

      &:hover {
        border-color: rgba(255, 129, 66, 0.4);
        background-color: rgba(255, 129, 66, 0.05);
      }

      &--selected {
        border-color: var(--q-primary);
        background-color: rgba(255, 129, 66, 0.08);

        .sport-label {
          color: var(--q-primary);
          font-weight: 600;
        }

        .sport-radio__circle {
          border-color: var(--q-primary);
        }

        .sport-radio__dot {
          background-color: var(--q-primary);
        }
      }

      .sport-radio {
        margin-right: 8px;
        flex-shrink: 0;

        &__circle {
          width: 16px;
          height: 16px;
          border: 1.5px solid #d1d5db;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: border-color 0.2s ease;
        }

        &__dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          transition: background-color 0.2s ease;
        }
      }

      .sport-label {
        font-size: 13px;
        font-weight: 500;
        color: #374151;
        transition: all 0.2s ease;
        line-height: 1.2;
        text-align: left;
      }
    }
  }
}

// Multi-Select Skill Level Styles - Smaller & Primary Color
.skill-level-multi {
  .skill-chips-row {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .skill-chip {
      border-radius: 8px;
      font-size: 12px;
      font-weight: 500;
      padding: 6px 10px;
      min-height: 28px;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      // Default state
      &:not(.q-chip--selected) {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        border-color: #e9ecef;

        &:hover {
          background-color: #e9ecef !important;
          color: #495057 !important;
          border-color: #dee2e6;
          transform: translateY(-1px);
        }
      }

      // Selected state with primary color
      &.q-chip--selected {
        background-color: #ff8142 !important;
        color: white !important;
        box-shadow: 0 2px 6px rgba(255, 129, 66, 0.3);
        transform: translateY(-1px);

        :deep(.q-icon) {
          color: white !important;
        }
      }

      // Icon styling for checkmarks
      :deep(.q-chip__content) {
        display: flex;
        align-items: center;

        .q-icon {
          margin-right: 3px;
          font-size: 12px;
        }
      }
    }
  }
}

// Enhanced Date Selection Styles
.date-selection-container {
  .quick-date-options {
    .date-toggle-enhanced {
      width: 100%;
      border-radius: 12px;
      background-color: #f8f9fa;
      box-shadow: none !important;

      :deep(.q-btn-toggle__btn) {
        flex: 1;
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
        min-height: 44px;
        transition: all 0.2s ease;

        &:not(.q-btn--active) {
          background-color: transparent;
          color: #6c757d;

          &:hover {
            background-color: #e9ecef;
            color: #495057;
          }
        }

        &.q-btn--active {
          background-color: var(--q-primary);
          color: white;
          box-shadow: 0 2px 8px rgba(255, 129, 66, 0.25);
          transform: translateY(-1px);
        }
      }
    }
  }

  .custom-date-section {
    .custom-date-input {
      :deep(.q-field__control) {
        border-radius: 12px;
        min-height: 48px;
      }

      :deep(.q-field__native) {
        cursor: pointer;
        font-weight: 500;
      }

      &:hover {
        :deep(.q-field__control) {
          border-color: var(--q-primary);
        }
      }
    }
  }

  .selected-date-display {
    padding: 8px 12px;
    background-color: #f0f9ff;
    border-radius: 8px;
    border-left: 3px solid var(--q-primary);
  }
}

// Date Picker Dialog Styles
.date-picker-card {
  border-radius: 16px;
  overflow: hidden;

  :deep(.q-date) {
    box-shadow: none;

    .q-date__header {
      background-color: var(--q-primary);
      color: white;
      padding: 16px;
    }

    .q-date__view {
      padding: 16px;
    }

    .q-btn.q-date__calendar-item {
      border-radius: 8px;

      &.q-date__calendar-item--in {
        background-color: var(--q-primary);
        color: white;
      }

      &:hover:not(.q-date__calendar-item--in) {
        background-color: rgba(255, 129, 66, 0.1);
      }
    }

    .q-date__navigation {
      .q-btn {
        border-radius: 8px;
      }
    }
  }
}

// Mobile responsive adjustments
@media (max-width: 599px) {
  .sport-selection-radio {
    .sport-options-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;

      .sport-option-item {
        padding: 8px 6px;
        min-height: 40px;

        .sport-radio {
          margin-right: 6px;

          &__circle {
            width: 14px;
            height: 14px;
            border-width: 1px;
          }

          &__dot {
            width: 6px;
            height: 6px;
          }
        }

        .sport-label {
          font-size: 12px;
          line-height: 1.1;
        }
      }
    }
  }

  .skill-level-multi {
    .skill-chips-row {
      gap: 4px;

      .skill-chip {
        font-size: 11px;
        padding: 4px 8px;
        min-height: 24px;
        border-radius: 6px;

        :deep(.q-chip__content) {
          .q-icon {
            font-size: 10px;
            margin-right: 2px;
          }
        }
      }
    }
  }

  .date-selection-container {
    .quick-date-options {
      .date-toggle-enhanced {
        :deep(.q-btn-toggle__btn) {
          padding: 10px 12px;
          font-size: 13px;
          min-height: 40px;
        }
      }
    }

    .custom-date-section {
      .custom-date-input {
        :deep(.q-field__control) {
          min-height: 44px;
        }
      }
    }
  }

  .date-picker-card {
    margin: 0;
    border-radius: 16px 16px 0 0;

    :deep(.q-date) {
      .q-date__header {
        padding: 12px;
      }

      .q-date__view {
        padding: 12px;
      }
    }
  }
}

// Tablet and desktop optimizations
@media (min-width: 600px) {
  .sport-selection-radio {
    .sport-options-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;

      .sport-option-item {
        padding: 12px 10px;
        min-height: 48px;

        .sport-radio {
          margin-right: 10px;

          &__circle {
            width: 18px;
            height: 18px;
            border-width: 2px;
          }

          &__dot {
            width: 10px;
            height: 10px;
          }
        }

        .sport-label {
          font-size: 14px;
          line-height: 1.3;
        }
      }
    }
  }

  .skill-level-multi {
    .skill-chips-row {
      gap: 6px;

      .skill-chip {
        font-size: 13px;
        padding: 6px 12px;
        min-height: 30px;
        border-radius: 8px;

        :deep(.q-chip__content) {
          .q-icon {
            font-size: 12px;
            margin-right: 4px;
          }
        }
      }
    }
  }

  .date-selection-container {
    .quick-date-options {
      .date-toggle-enhanced {
        :deep(.q-btn-toggle__btn) {
          padding: 14px 20px;
          font-size: 15px;
          min-height: 48px;
        }
      }
    }
  }

  .date-picker-card {
    min-width: 320px;
    border-radius: 16px;
  }
}

// Optimized Compact Location Section
.location-compact-row {
  .location-select-compact {
    :deep(.q-field__control) {
      border-radius: 12px;
      min-height: 48px;
    }

    :deep(.q-field__append) {
      padding-right: 4px;
    }

    .current-location-btn {
      width: 32px;
      height: 32px;
      margin-left: 4px;

      &:hover {
        background: rgba(255, 129, 66, 0.1);
      }
    }
  }
}

// Optimized Mobile-First Footer Actions using Quasar CSS
.sticky-footer {
  .q-card-actions {
    border-top: 1px solid var(--q-separator-color);
    background: var(--q-grey-1);
    padding: 16px;

    .q-btn {
      border-radius: 12px;
      font-weight: 600;
      min-height: 48px;
      font-size: 16px;
      transition: all 0.2s ease;

      // Reset button styling
      &.q-btn--outline {
        border: 2px solid var(--q-grey-4);
        background: white;

        &:hover {
          background: var(--q-grey-2);
          border-color: var(--q-grey-6);
          transform: translateY(-1px);
        }
      }

      // Apply button styling
      &.q-btn--unelevated:not(.q-btn--outline) {
        box-shadow: 0 4px 12px rgba(255, 129, 66, 0.25);
        font-weight: 700;

        &:hover {
          box-shadow: 0 6px 16px rgba(255, 129, 66, 0.35);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(255, 129, 66, 0.3);
        }
      }
    }
  }
}

// Tablet and desktop enhancements
@media (min-width: $breakpoint-sm-min) {
  .sticky-footer {
    .q-card-actions {
      padding: 20px 24px;

      .q-btn {
        min-height: 44px;
        font-size: 15px;
        padding: 10px 32px;
      }
    }
  }
}
</style>
