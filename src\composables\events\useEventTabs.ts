import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

export function useEventTabs() {
  const route = useRoute();
  const router = useRouter();
  const currentTab = ref('discovery');
  const eventCount = ref(2);

  onMounted(() => {
    const path = route.path;
    currentTab.value = path.includes('my-events') ? 'my-events' : 'discovery';
  });

  watch(currentTab, (newTab) => {
    router.push(`/events/${newTab}`);
  });

  watch(
    () => route.path,
    (newPath) => {
      if (newPath.includes('my-events')) {
        currentTab.value = 'my-events';
      } else {
        currentTab.value = 'discovery';
      }
    },
  );

  return {
    currentTab,
    eventCount,
  };
}
