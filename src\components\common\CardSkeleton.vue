<!-- Skeleton Loading State -->
<template v-if="loading">
  <div class="card-event">
    <q-skeleton
      height="200px"
      class="q-mb-sm"
      animation="wave"
      square
      style="border-top-left-radius: 12px; border-top-right-radius: 12px"
    />
    <q-card-section class="column justify-between" style="gap: 10px">
      <q-skeleton type="text" width="85%" class="text-subtitle1" />

      <div class="column items-start" style="gap: 10px">
        <div class="row items-center" style="gap: 5px; width: 100%">
          <q-skeleton type="rect" size="16px" />
          <q-skeleton type="text" width="60%" />
        </div>
        <div class="row items-center" style="gap: 5px; width: 100%">
          <q-skeleton type="rect" size="16px" />
          <q-skeleton type="text" width="70%" />
        </div>
      </div>

      <div class="row items-center justify-between">
        <q-skeleton type="text" width="40%" />
        <q-skeleton type="QBtn" width="80px" />
      </div>
    </q-card-section>
  </div>
</template>
