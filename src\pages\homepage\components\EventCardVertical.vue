<template>
  <CardVertical 
    :image-url="photoUrl" 
    :image-alt="title"
    @click="$emit('click')"
  >
    <template #badges>
      <div v-if="badges?.length" class="badges-container">
        <div 
          v-for="(badge, index) in badges" 
          :key="index"
          class="status-badge" 
          :style="getBadgeStyles(badge)"
        >
          {{ badge.text }}
        </div>
      </div>
    </template>

    <template #favorite>
      <q-btn
        v-if="showFavorite"
        round
        flat
        dense
        size="sm"
        class="favorite-btn"
        @click.stop="handleFavoriteClick"
      >
        <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="16px" color="white" />
      </q-btn>
    </template>

    <template #header>
      <div class="event-title">{{ title }}</div>
      <div v-if="subtitle" class="event-subtitle">{{ subtitle }}</div>
    </template>
    
    <template #price>
      <div class="price-row" v-if="formattedPriceDisplay.current || formattedPriceDisplay.original">
        <div v-if="formattedPriceDisplay.original" class="original-price">{{ formattedPriceDisplay.original }}</div>
        <div class="price">{{ formattedPriceDisplay.current }}</div>
        <div class="rating" v-if="rating">
          <q-icon name="star" color="amber" size="14px" class="q-mr-xs" />
          <span>{{ rating }}</span>
        </div>
      </div>
    </template>
    
    <template #meta>
      <div class="datetime-row" v-if="formattedDateTime">
        <q-icon name="access_time" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ formattedDateTime }}</span>
      </div>
      
      <div class="location-row" v-if="location">
        <q-icon name="location_on" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ location }}</span>
        <span class="distance-chip" v-if="distance">{{ distance }}</span>
      </div>
      
      <div class="participants-row" v-if="participantsText">
        <q-icon name="group" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ participantsText }}</span>
        <span class="status-text" v-if="isAlmostFull">Sắp đầy!</span>
      </div>
    </template>
    
    <template #actions>
      <q-btn
        v-if="showBookButton"
        color="primary"
        :label="bookBtnText"
        class="full-width"
        @click.stop="$emit('book')"
      />
    </template>
  </CardVertical>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import CardVertical from 'src/components/CardVertical.vue';
import { useCard } from 'src/composables/useCard';
import type { Badge } from 'src/components/BaseCard.vue';

interface Props {
  // Basic info
  title: string;
  subtitle?: string;
  photoUrl?: string;
  
  // Badges
  badges?: Badge[];
  
  // Price and participants
  originalPrice?: number;
  price?: number;
  currentParticipants?: number;
  maxParticipants?: number;
  
  // Date and time
  date?: string;
  startTime?: string;
  endTime?: string;
  
  // Location
  location?: string;
  distance?: string;
  
  // Rating
  rating?: number;
  
  // UI Controls
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  photoUrl: '/images/image_default.jpg',
  badges: () => [],
  showFavorite: true,
  showBookButton: true,
  bookBtnText: 'Đặt ngay',
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

// Use shared card logic
const {
  isFavorite,
  toggleFavorite,
  getBadgeStyles,
  formattedDateTime,
  participantsText,
  formattedPriceDisplay
} = useCard(props);

const handleFavoriteClick = (event: Event) => {
  const newFavoriteState = toggleFavorite(event);
  emit('favorite', newFavoriteState);
};

const isAlmostFull = computed(() => {
  if (!props.currentParticipants || !props.maxParticipants) return false;
  return props.currentParticipants / props.maxParticipants >= 0.8;
});
</script>

<style lang="scss" scoped>
.badges-container {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  max-width: calc(100% - 16px);
}

.status-badge {
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.favorite-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.event-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  color: #212529;
  margin-bottom: 4px;
}

.event-subtitle {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  
  .original-price {
    font-size: 13px;
    color: #6c757d;
    text-decoration: line-through;
    margin-right: 6px;
  }
  
  .price {
    font-size: 16px;
    font-weight: 700;
    color: #ff8142;
  }
  
  .rating {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
  }
}

.datetime-row,
.location-row,
.participants-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6c757d;
  
  .distance-chip {
    margin-left: auto;
    font-size: 11px;
    background: #f1f5f9;
    padding: 2px 4px;
    border-radius: 4px;
  }
  
  .status-text {
    margin-left: auto;
    font-size: 11px;
    color: #e74c3c;
    font-weight: 500;
  }
}
</style>
