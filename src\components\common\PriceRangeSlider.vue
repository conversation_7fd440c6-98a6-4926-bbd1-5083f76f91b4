<template>
  <div class="price-range-slider">
    <div class="q-mb-sm">
      <div class="text-body2 text-weight-medium">{{ label }}</div>
      <div class="row items-center justify-between text-caption text-grey-7">
        <span>{{ formatPrice(props.min) }}</span>
        <span>{{ formatPrice(props.max) }}</span>
      </div>
    </div>

    <q-range
      v-model="rangeValue"
      :min="min"
      :max="max"
      :step="step"
      :label="showLabels"
      :label-value="`${formatPrice(rangeValue.min)} - ${formatPrice(rangeValue.max)}`"
      color="primary"
      track-color="grey-3"
      thumb-color="primary"
      @update:model-value="onRangeChange"
    />

    <div class="row items-center justify-between q-mt-sm text-caption text-primary">
      <span>{{ formatPrice(rangeValue.min) }}</span>
      <span>{{ formatPrice(rangeValue.max) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

type RangeValue = {
  min: number;
  max: number;
};

type Props = {
  modelValue?: string | null;
  min?: number;
  max?: number;
  step?: number;
  label?: string;
  showLabels?: boolean;
};

type Emits = {
  (e: 'update:modelValue', value: string | null): void;
};

const props = withDefaults(defineProps<Props>(), {
  min: 0,
  max: 1000000,
  step: 50000,
  label: 'Khoảng giá',
  showLabels: false,
});

const emit = defineEmits<Emits>();

// Initialize range value
const rangeValue = ref<RangeValue>({
  min: props.min,
  max: props.max,
});

// Watch for external model value changes
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      const [minStr, maxStr] = newValue.split('-');
      const min = Number(minStr);
      const max = Number(maxStr);
      if (!isNaN(min) && !isNaN(max)) {
        rangeValue.value = { min, max };
      }
    } else {
      rangeValue.value = { min: props.min, max: props.max };
    }
  },
  { immediate: true },
);

// Format price to VND currency
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

// Handle range change
const onRangeChange = (value: RangeValue) => {
  rangeValue.value = value;

  // Emit the value in the same format as the original implementation
  const rangeString = `${value.min}-${value.max}`;
  emit('update:modelValue', rangeString);
};

// Reset to default range
const reset = () => {
  rangeValue.value = { min: props.min, max: props.max };
  emit('update:modelValue', null);
};

// Expose reset method
defineExpose({
  reset,
});
</script>

<style scoped>
.price-range-slider {
  min-width: 200px;
}

:deep(.q-slider__track) {
  height: 4px;
}

:deep(.q-slider__thumb) {
  width: 20px;
  height: 20px;
}

:deep(.q-slider__selection) {
  background: var(--q-primary);
}

:deep(.q-slider__label) {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: var(--q-primary);
  color: white;
}
</style>
