import { CalendarCheck, Users } from 'lucide-vue-next';
import type { Component } from 'vue';

export interface Event {
  id: string | number;
  title: string;
  photoUrl?: string;
  sport: string;
  badge?: string;
  originalPrice?: number;
  price: number;
  rating?: number;
  location: string;
  distance?: string;
  date: string;
  startTime: string;
  endTime?: string;
  currentParticipants?: number;
  maxParticipants?: number;
}

export interface Venue {
  id: string | number;
  name: string;
  address: string;
  photoUrl: string;
  distance: number;
  price: string;
  rating: number;
  ratingCount?: number;
  sports: string[];
  amenities: string[];
}

export interface TrendingCategory {
  id: string;
  name: string;
  icon: Component;
  growth: number;
  bgColor: string;
}

// Stats data
export const statsData = [
  {
    icon: CalendarCheck,
    value: '1,234',
    label: 'Sự kiện',
    color: '#3b82f6',
  },
  {
    icon: Users,
    value: '45K',
    label: 'Thành viên',
    color: '#10b981',
  },
  {
    icon: CalendarCheck,
    value: '567',
    label: 'Sân thể thao',
    color: '#8b5cf6',
  },
  {
    icon: Users,
    value: '4.9★',
    label: 'Đánh giá',
    color: '#f59e0b',
  },
];

// Trending categories data
export const trendingCategoriesData: TrendingCategory[] = [
  {
    id: 'football',
    name: 'Bóng đá',
    icon: CalendarCheck,
    growth: 12,
    bgColor: '#3b82f6',
  },
  {
    id: 'yoga',
    name: 'Yoga',
    icon: Users,
    growth: 22,
    bgColor: '#8b5cf6',
  },
  {
    id: 'tennis',
    name: 'Tennis',
    icon: CalendarCheck,
    growth: 18,
    bgColor: '#f59e0b',
  },
  {
    id: 'swimming',
    name: 'Bơi lội',
    icon: Users,
    growth: 15,
    bgColor: '#10b981',
  },
];

// Ticket events data
export const ticketEventsData: Event[] = [
  {
    id: '1',
    title: 'Giải Bóng Đá Mùa Hè 2024',
    photoUrl: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=600',
    sport: 'football',
    badge: 'Còn 1 chỗ',
    originalPrice: 200000,
    price: 150000,
    rating: 4.9,
    location: 'SVĐ Mỹ Đình',
    distance: '2.1km',
    date: '2023-06-29',
    startTime: '15:00',
    endTime: '18:00',
    currentParticipants: 15,
    maxParticipants: 20,
  },
  {
    id: '2',
    title: 'Giải Cầu Lông Chuyên Nghiệp',
    photoUrl: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=600',
    sport: 'badminton',
    badge: '2 ngày trước',
    originalPrice: 300000,
    price: 180000,
    rating: 4.8,
    location: 'Cung thể thao Quận Nguyễn',
    distance: '1.8km',
    date: '2023-06-30',
    startTime: '10:00',
    endTime: '16:00',
    currentParticipants: 32,
    maxParticipants: 40,
  },
  {
    id: '3',
    title: 'Yoga Flow buổi sáng',
    photoUrl: 'https://images.unsplash.com/photo-1593811167562-9cef47bfc4d7?w=600',
    sport: 'yoga',
    badge: 'Sắp khai trương',
    price: 120000,
    rating: 4.7,
    location: 'Công viên Thống Nhất',
    distance: '900m',
    date: '2023-07-01',
    startTime: '07:00',
    endTime: '08:30',
    currentParticipants: 8,
    maxParticipants: 15,
  },
];

// Exchange events data
export const exchangeEventsData: Event[] = [
  {
    id: 'e1',
    title: 'Bóng đá phủi cuối tuần',
    photoUrl: 'https://images.unsplash.com/photo-1540379708242-14a809bef650?w=600',
    sport: 'football',
    badge: 'Sắp đầy',
    price: 50000,
    location: 'Sân Thanh Xuân',
    distance: '1.5km',
    date: '2023-06-25',
    startTime: '18:00',
    endTime: '20:00',
    currentParticipants: 8,
    maxParticipants: 10,
  },
  {
    id: 'e2',
    title: 'Tennis đổi chuyên nghiệp',
    photoUrl: 'https://images.unsplash.com/photo-1595435934249-5df7ed86e1c0?w=600',
    sport: 'tennis',
    badge: 'Sắp đầy',
    price: 120000,
    rating: 4.8,
    location: 'CLB Tennis Ciputra',
    distance: '4.2km',
    date: '2023-06-28',
    startTime: '16:00',
    endTime: '18:00',
    currentParticipants: 1,
    maxParticipants: 1,
  },
  {
    id: 'e3',
    title: 'Nhóm chạy bộ buổi sáng',
    photoUrl: 'https://images.unsplash.com/photo-1539966903171-89770f33f468?w=600',
    sport: 'running',
    badge: 'Miễn phí',
    price: 0,
    location: 'Hồ Gươm',
    distance: '2.5km',
    date: '2023-06-30',
    startTime: '06:00',
    endTime: '07:30',
    currentParticipants: 5,
    maxParticipants: 10,
  },
];

// Venues data
export const venuesData: Venue[] = [
  {
    id: '1',
    name: 'Sân bóng Mỹ Đình Premium',
    address: '123 Mỹ Đình, Nam Từ Liêm',
    photoUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600',
    distance: 2.1,
    price: '400.000đ/giờ',
    rating: 4.9,
    ratingCount: 234,
    sports: ['football'],
    amenities: ['Parking', 'Shower', 'Café'],
  },
  {
    id: '2',
    name: 'Tennis Court Ciputra',
    address: 'Ciputra, Tây Hồ',
    photoUrl: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=600',
    distance: 3.5,
    price: '180.000đ/giờ',
    rating: 4.7,
    ratingCount: 156,
    sports: ['tennis'],
    amenities: ['Equipment', 'AC'],
  },
  {
    id: '3',
    name: 'Yoga Studio Zen',
    address: 'Hoàn Kiếm',
    photoUrl: 'https://images.unsplash.com/photo-1593810450967-f9c2f7f6f9b5?w=600',
    distance: 1.2,
    price: '120.000đ/giờ',
    rating: 4.8,
    ratingCount: 189,
    sports: ['yoga'],
    amenities: ['Props', 'Music'],
  },
];
