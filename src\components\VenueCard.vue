<template>
  <q-card class="venue-card" @click="$emit('click')">
    <div class="venue-content">
      <!-- Venue Image -->
      <div class="venue-image-container">
        <q-img
          :src="venue.photoUrl"
          class="venue-image"
          fit="cover"
          loading="lazy"
        >
          <!-- Status Badge -->
          <div class="status-badge" :class="statusClass">
            <div class="status-dot"></div>
            <span class="status-text">{{ statusText }}</span>
          </div>
        </q-img>
      </div>

      <!-- Venue Info -->
      <div class="venue-info">
        <!-- Header: Name and Rating -->
        <div class="venue-header">
          <div class="venue-name">{{ venue.name }}</div>
          <div class="rating-container">
            <Star :size="12" class="rating-icon" />
            <span class="rating-value">{{ venue.rating }}</span>
          </div>
        </div>

        <!-- Location -->
        <div class="venue-location">
          <MapPin :size="12" class="location-icon" />
          <span class="location-text">{{ venue.distance }}km • {{ venue.address }}</span>
        </div>

        <!-- Footer: Price and Sports -->
        <div class="venue-footer">
          <div class="price-info">
            <span class="price">{{ venue.price }}</span>
          </div>
        </div>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { MapPin, Star } from 'lucide-vue-next';
import { computed } from 'vue';
import type { IVenueProps } from 'src/pages/homepage/types/index';

interface Props {
  venue: IVenueProps;
}

defineProps<Props>();

defineEmits<{
  click: [];
}>();

// Computed properties
const statusText = computed(() => {
  const currentHour = new Date().getHours();
  const isOpen = currentHour >= 6 && currentHour <= 22; // Assume venues open 6AM-10PM
  return isOpen ? 'Open' : 'Closed';
});

const statusClass = computed(() => {
  const currentHour = new Date().getHours();
  const isOpen = currentHour >= 6 && currentHour <= 22;
  return isOpen ? 'status-open' : 'status-closed';
});

</script>

<style lang="scss" scoped>
// Design tokens
$primary-color: #ff8142;
$text-primary: #212529;
$text-secondary: #6c757d;
$text-muted: #9ca3af;
$success-color: #10b981;
$error-color: #ef4444;
$border-radius: 12px;
$border-radius-sm: 6px;
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
$transition: all 0.2s ease;

.venue-card {
  border-radius: $border-radius;
  overflow: hidden;
  cursor: pointer;
  transition: $transition;
  border: 1px solid #f1f5f9;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  .venue-content {
    display: flex;
    padding: $spacing-md;
    gap: $spacing-md;

    .venue-image-container {
      position: relative;
      flex-shrink: 0;

      .venue-image {
        width: 80px;
        height: 80px;
        border-radius: $border-radius;
      }

      .status-badge {
        position: absolute;
        top: $spacing-xs;
        right: $spacing-xs;
        display: flex;
        align-items: center;
        gap: 3px;
        padding: 2px 6px;
        border-radius: 8px;
        backdrop-filter: blur(4px);
        font-size: 9px;
        font-weight: 600;
        text-transform: uppercase;

        .status-dot {
          width: 4px;
          height: 4px;
          border-radius: 50%;
        }

        .status-text {
          line-height: 1;
        }

        &.status-open {
          background: rgba(16, 185, 129, 0.9);
          color: white;

          .status-dot {
            background: white;
          }
        }

        &.status-closed {
          background: rgba(239, 68, 68, 0.9);
          color: white;

          .status-dot {
            background: white;
          }
        }
      }
    }

    .venue-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0; // Prevent flex item from overflowing

      .venue-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: $spacing-sm;
        margin-bottom: $spacing-xs;

        .venue-name {
          font-size: 15px;
          font-weight: 600;
          color: $text-primary;
          line-height: 1.3;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .rating-container {
          display: flex;
          align-items: center;
          gap: 3px;
          flex-shrink: 0;
          padding: 2px 6px;
          background: #fff7ed;
          border-radius: $border-radius-sm;

          .rating-icon {
            color: $primary-color;
            fill: $primary-color;
          }

          .rating-value {
            font-size: 12px;
            font-weight: 600;
            color: $primary-color;
          }
        }
      }

      .venue-location {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        margin-bottom: $spacing-sm;

        .location-icon {
          color: $primary-color;
          flex-shrink: 0;
        }

        .location-text {
          font-size: 12px;
          color: $text-secondary;
          line-height: 1.3;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .venue-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: $spacing-sm;

        .price-info {
          .price {
            font-size: 14px;
            font-weight: 700;
            color: $primary-color;
          }
        }

        .sports-info {
          .sports-chips {
            display: flex;
            gap: $spacing-xs;

            .sport-chip {
              font-size: 10px;
              padding: 2px 4px;
              background: #f8f9fa;
              color: $text-secondary;
              border-radius: $border-radius-sm;

              &.more-sports {
                background: $primary-color;
                color: white;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}

// Mobile optimizations
@media (max-width: 600px) {
  .venue-card {
    .venue-content {
      padding: $spacing-sm;
      gap: $spacing-sm;

      .venue-image-container {
        .venue-image {
          width: 64px;
          height: 64px;
        }

        .status-badge {
          font-size: 8px;
          padding: 1px 4px;
        }
      }

      .venue-info {
        .venue-header {
          .venue-name {
            font-size: 14px;
          }

          .rating-container {
            .rating-value {
              font-size: 11px;
            }
          }
        }

        .venue-location {
          .location-text {
            font-size: 11px;
          }
        }

        .venue-footer {
          .price-info {
            .price {
              font-size: 13px;
            }
          }

          .sports-info {
            .sports-chips {
              .sport-chip {
                font-size: 9px;
              }
            }
          }
        }
      }
    }
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .venue-card {
    transition: none;
  }
}
</style>
