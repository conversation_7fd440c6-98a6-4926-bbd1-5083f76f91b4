<template>
  <q-page-container>
    <q-page padding style="padding-top: 80px">
      <q-page-sticky expand position="top" class="main-header bg-white" style="z-index: 1000">
        <q-toolbar-title class="full-width row" style="max-width: 690px; margin: 0 auto;">
          <q-card-section class="row items-center">
            <q-btn to="/events" outline rounded dense color="grey-5" >
              <ArrowLeft :size="20" color="grey"/>
            </q-btn>
            <span class="text-weight-regular q-ml-sm" :class="$q.screen.lt.sm ? 'text-body1' : 'text-h5'">Tạo sự kiện</span>
          </q-card-section>
          <q-space />
          <q-card-section>
            <q-select
              v-model="form.activeSport"
              :options="sportOptions"
              option-label="name"
              rounded
              filled
              dense
              input-class="sports-select"
              dropdown-icon="expand_more"
              behavior="menu"
            >
              <template v-slot:option="scope">
                <q-item dense v-bind="scope.itemProps">
                  <div class="row no-wrap items-center">
                    <div class="text-body1 q-mr-sm text-weight-regular">{{ scope.opt.icon }}</div>
                    <div>{{ scope.opt.name }}</div>
                  </div>
                </q-item>
              </template>

              <template v-slot:selected>
                <div class="row no-wrap items-center q-gutter-x-sm w-full">
                  <div style="font-size: 16px">{{ form.activeSport?.icon }}</div>
                  <div>{{ form.activeSport?.name }}</div>
                </div>
              </template>
            </q-select>
          </q-card-section>
        </q-toolbar-title>
        <q-separator />
      </q-page-sticky>
      <div class="q-mb-xl">
        <!-- Simple Event Type Selection -->
        <div class="event-type-section q-mb-md">
          <!-- Horizontal Scrollable Tags -->
          <div class="event-type-scroll">
            <q-chip
              v-for="eventType in eventTypes"
              :key="eventType"
              :label="eventType"
              :color="form.activeTab === eventType ? 'primary' : 'black'"
              :text-color="form.activeTab === eventType ? 'primary' : 'black'"
              clickable
              outline
              icon="tag"
              @click="form.activeTab = eventType"
            />
          </div>
        </div>
        <!-- Form Content Container -->
        <q-form class="q-pb-md">
          <q-card class="q-mb-md">
            <q-card-section>
              <div :class="$q.screen.lt.sm ? 'text-body1 q-mb-sm' : 'text-h6 q-mb-md'">Thông tin chung<span class="text-subtitle1 text-red">*</span></div>
              <q-item class="column q-pa-none">
                <q-input
                  v-model="form.name"
                  outlined
                  label="Tên sự kiện"
                  class="q-mb-md"
                >
                </q-input>
              </q-item>
              <q-item class="column q-pa-none q-mb-md">
                <q-select
                  v-model="form.location"
                  outlined
                  option-value="name"
                  label="Địa điểm"
                  dropdown-icon="expand_more"
                  :popup-content-class="'q-select-dialog-content'"
                  :options="filteredCities"
                >
                  <template v-slot:prepend>
                    <MapPin :size="20" color="#9ca3af" />
                  </template>

                  <template v-slot:option="{ itemProps, opt }">
                    <q-item
                      v-bind="itemProps"
                      class="text-black text-body1 q-py-md"
                      style="border-bottom: 1px solid #e5e7eb"
                    >
                      <q-item-section avatar style="min-width: 20px">
                        <MapPin :size="20" color="#9ca3af" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ opt.name }}</q-item-label>
                        <q-item-label caption>{{ opt.distance }} away</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>

                  <template v-slot:selected>
                    <div v-if="form.location">
                      <div>{{ (form.location as any).name }}</div>
                    </div>
                  </template>

                  <template v-slot:before-options>
                    <q-input
                      outlined
                      dense
                      small
                      placeholder="Tìm địa chỉ ..."
                      v-model="locationSearch"
                      class="q-pa-sm"
                    />
                    <q-separator />
                  </template>

                  <template v-slot:no-option>
                    <q-input
                      outlined
                      dense
                      placeholder="Search location..."
                      v-model="locationSearch"
                      class="q-pa-sm"
                    />
                  </template>
                </q-select>
              </q-item>
              <q-item class="column q-pa-none">
                <q-input
                  v-model="form.description"
                  type="textarea"
                  outlined
                  label="Mô tả sự kiện"
                  class="q-mb-md"
                  :rows="$q.screen.lt.sm ? 3 : 4"
                />
              </q-item>
            </q-card-section>
          </q-card>

          <q-card class="q-mb-md">
            <q-card-section>
              <div :class="$q.screen.lt.sm ? 'text-body1 q-mb-sm' : 'text-h6 q-mb-md'">Thời gian<span class="text-subtitle1 text-red">*</span></div>
              <q-item class="column q-pa-none">
                <q-input
                  v-model="form.date"
                  outlined
                  type="date"
                  label="Ngày tổ chức"
                  lazy-rules
                  placeholder="dd/mm/yyyy"
                  class="q-mb-md"
                >
                </q-input>
              </q-item>
              <q-item class="column q-pa-none">
                <div class="row q-col-gutter-md">
                  <div class="col-6">
                    <q-input
                      v-model="form.startTime"
                      outlined
                      type="time"
                      lazy-rules
                      label="Giờ bắt đầu"
                      placeholder="Nhập thời gian..."
                      class="q-mb-md"
                    >
                    </q-input>
                  </div>
                  <div class="col-6">
                    <q-input
                      v-model="form.endTime"
                      outlined
                      type="time"
                      label="Giờ kết thúc"
                      lazy-rules
                      placeholder="Nhập thời gian..."
                      class="q-mb-md"
                    >
                    </q-input>
                  </div>
                </div>
              </q-item>
            </q-card-section>
          </q-card>

          <q-card class="q-mb-md">
            <q-card-section>
              <div :class="$q.screen.lt.sm ? 'text-body1 q-mb-sm' : 'text-h6 q-mb-md'">Yêu cầu thành viên <span class="text-subtitle1 text-red">*</span></div>
              <q-item class="column q-pa-none">
                <div class="row q-col-gutter-md">
                  <div class="col-6">
                    <q-input
                      v-model="form.slots"
                      outlined
                      type="number"
                      lazy-rules
                      label="Số người cần tuyển"
                      placeholder="0"
                    >
                    </q-input>
                  </div>
                  <div class="col-6">
                    <q-select
                      v-model="form.gender"
                      outlined
                      label="Giới tính"
                      type="number"
                      lazy-rules
                      behavior="menu"
                      dropdown-icon="expand_more"
                      value-field="value"
                      label-field="label"
                      :options="[{ label: 'Cả nam và nữ', value: 'all' }, { label: 'Chỉ nam', value: 'male' }, { label: 'Chỉ nữ', value: 'female' }]"
                    >
                    </q-select>
                  </div>
                  <div class="col-12">
                    <q-select
                      v-model="form.selectedLevels"
                      outlined
                      multiple
                      label="Trình độ"
                      lazy-rules
                      placeholder="Chọn trình độ"
                      behavior="menu"
                      dropdown-icon="expand_more"
                      class="q-mb-md"
                      :options="skillLevels"
                      use-chips
                    >
                    </q-select>
                  </div>
                </div>
              </q-item>
            </q-card-section>
          </q-card>

          <q-card class="q-mb-md">
            <q-card-section>
              <div :class="$q.screen.lt.sm ? 'text-body1 q-mb-sm' : 'text-h6 q-mb-md'">Chi phí tham gia<span class="text-subtitle1 text-red">*</span></div>
              <q-item class="column q-pa-none">
                <div class="row" :class="$q.screen.lt.sm ? '' : 'q-col-gutter-md'">
                  <div class="lg:col-6 col-12">
                    <q-input
                      v-model="form.priceMale"
                      outlined
                      type="number"
                      lazy-rules
                      label="Giá vé cho nam"
                      placeholder="0"
                      mask="#.##"
                      :disable="form.gender === 'female' || form.isFree || form.isAgreement"
                      :rules="[val => val > 0 || 'Giá vé phải lớn hơn 0']"
                    >
                    <template v-slot:prepend>
                       <DollarSign :size="20" color="#9ca3af" />
                    </template>
                    </q-input>
                  </div>
                  <div class="lg:col-6 col-12">
                    <q-input
                      v-model="form.priceFemale"
                      outlined
                      type="number"
                      label="Giá vé cho nữ"
                      lazy-rules
                      placeholder="0"
                      :disable="form.gender === 'male' || form.isFree || form.isAgreement"
                      mask="#.##"
                      :rules="[val => val > 0 || 'Giá vé phải lớn hơn 0']"
                    >
                    <template v-slot:prepend>
                      <DollarSign :size="20" color="#9ca3af" />
                    </template>
                    </q-input>
                  </div>
                  <div class="col-12 q-gutter-x-md" :class="$q.screen.lt.sm ? '' : 'q-pa-md'">
                    <q-checkbox
                      v-model="form.isAgreement"
                      label="Thoả thuận"
                      class="q-mb-md"
                      dense
                    />
                    <q-checkbox
                      v-model="form.isFree"
                      label="Miễn phí"
                      class="q-mb-md"
                      dense
                    />
                  </div>
                </div>
              </q-item>
            </q-card-section>
          </q-card>

          <q-card class="q-mb-md">
            <q-card-section>
              <q-item class="column q-pa-none q-mb-md">
                <div class="row items-center justify-between">
                  <div class="text-subtitle1 q-mb-sm">Ảnh sự kiện</div>
                  <div class="text-caption text-grey-7 q-mb-sm">Tùy chọn</div>
                </div>
                <div
                  v-if="form.images.length === 0"
                  class="upload-container q-pa-lg flex column items-center justify-center"
                  @click="triggerFileUpload"
                  @dragover.prevent="onDragOver"
                  @dragleave.prevent="onDragLeave"
                  @drop.prevent="onDrop"
                  :class="{ 'drag-over': isDragOver }"
                >
                  <q-icon name="camera_alt" size="28px" color="grey-7" />
                  <div class="text-subtitle2 q-mt-sm">Thêm ảnh</div>
                  <div class="text-caption text-grey-7">Kéo thả hoặc click để chọn</div>
                </div>
                <input
                  type="file"
                  multiple
                  ref="fileInput"
                  class="hidden"
                  @change="onFileSelected"
                />
                <div v-if="form.images.length > 0" class="row q-col-gutter-md q-mb-md">
                  <!-- Images Grid -->
                  <div class="col-6" v-for="(image, index) in imageUrls" :key="index">
                    <div class="preview-container">
                      <img :src="image" class="preview-image" />
                      <div class="close-btn row items-center justify-center">
                        <q-btn
                          round
                          dense
                          color="white"
                          text-color="black"
                          :size="$q.screen.lt.sm ? 'md' : 'lg'"
                          icon="close"
                          @click.stop="removeImage($event, index)"
                        />
                      </div>
                    </div>
                  </div>
                  <!-- Add More Button -->
                  <div class="col-6">
                    <div
                      class="upload-container q-pa-lg flex column items-center justify-center"
                      @click="triggerFileUpload"
                      style="height: 100%"
                    >
                      <q-icon name="camera_alt" size="28px" color="grey-7" />
                      <div class="text-subtitle2 q-mt-sm">Thêm ảnh</div>
                    </div>
                  </div>
                </div>
              </q-item>
            </q-card-section>
          </q-card>

          <q-card class="q-mb-md">
            <q-card-section>
              <q-item class="column q-pa-none">
                <q-item class="q-pa-none">
                  <q-item-section>
                    <div class="text-subtitle1 flex items-center">
                      <span>Riêng tư</span>
                      <q-icon name="info" size="16px" color="grey-7" class="q-ml-sm">
                        <q-tooltip>
                          Sự kiện riêng tư phù hợp cho nhóm nhỏ hoặc sự kiện nội bộ
                        </q-tooltip>
                      </q-icon>
                    </div>
                    <div class="text-caption text-grey-7">Chỉ người được mời mới thấy</div>
                  </q-item-section>
                  <q-item-section side>
                    <q-toggle
                      size="xl"
                      v-model="form.isPrivate"
                      :color="form.isPrivate ? 'red' : 'green'"
                      :icon="form.isPrivate ? 'lock' : 'public'"
                    />
                  </q-item-section>
                </q-item>
                <div class="text-caption text-grey-7 q-mt-sm">
                 
                </div>
              </q-item>
            </q-card-section>
          </q-card>
        </q-form>
      </div>
      <q-page-sticky expand position="bottom" class="bg-white z-top">
        <q-toolbar-title style="max-width: 690px; margin: 0 auto;">
          <q-card-section class="row q-col-gutter-md ">
            <div class="col-6">
              <q-btn
                label="Huỷ bỏ"
                outline
                :size="$q.screen.lt.sm ? 'md' : 'lg'"
                class="full-width"
                @click="onSubmit"
              />
            </div>
            <div class="col-6">
              <q-btn
                label="Tạo mới"
                color="primary"
                :size="$q.screen.lt.sm ? 'md' : 'lg'"
                class="full-width"
                @click="onSubmit"
              />
            </div>
          </q-card-section>
        </q-toolbar-title>
      </q-page-sticky>
    </q-page>
  </q-page-container>
</template>
<script setup lang="ts">
import { ArrowLeft, MapPin, DollarSign } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

const fileInput = ref(null);
const isDragOver = ref(false);
const locationSearch = ref('');

// Define the type at the top of your script section
interface Sport {
  name: string;
  icon: string;
}

const form = ref({
  name: '',
  description: '',
  date: '',
  startTime: '',
  endTime: '',
  isPrivate: false,
  participants: 10,
  slots: 10,
  gender: 'all',
  priceMale: undefined,
  priceFemale: undefined,
  isAgreement: false,
  isFree: false,
  image: null,
  activeTab: 'Tuyển Giao Lưu',
  location: null,
  selectedLevels: [] as string[],
  images: [] as File[],
  activeSport: null as Sport | null,
});

const sportOptions = ref([
  { name: 'Badminton', icon: '🏸' },
  { name: 'Pickleball', icon: '🏓' },
  { name: 'Football', icon: '⚽' },
  { name: 'Table Tennis', icon: '🏓' },
  { name: 'Basketball', icon: '🏀' },
  { name: 'Volleyball', icon: '🏐' },
]);

const eventTypes = ['#Tuyển Giao Lưu', '#Xé Vé', '#Bắt Đối', '#Pass Sân', '#Khác'];

const skillLevels = ref([
  { label: 'Mới', value: 'beginner' },
  { label: 'TB', value: 'intermediate' },
  { label: 'TBK', value: 'tbk' },
  { label: 'TBK+', value: 'tbk+' },
  { label: 'Nâng cao', value: 'advanced' },
]);

const cities = ref([
  { name: 'Central Sports Complex, New York, NY', distance: '0.5 km' },
  { name: 'Madison Square Garden, New York, NY', distance: '1.2 km' },
  { name: 'Brooklyn Sports Center, Brooklyn, NY', distance: '3.5 km' },
  { name: 'Queens Athletic Field, Queens, NY', distance: '5.7 km' },
  { name: 'Bronx Recreation Center, Bronx, NY', distance: '7.8 km' },
]);

const filteredCities = computed(() => {
  if (!locationSearch.value) return cities.value;
  return cities.value.filter((c) =>
    c.name.toLowerCase().includes(locationSearch.value.toLowerCase()),
  );
});

form.value.activeSport = sportOptions.value[0] as Sport;

const triggerFileUpload = () => {
  (fileInput.value as any).click();
};

const onFileSelected = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.type.includes('image')) {
        form.value.images.push(file);
      }
    }
  }
};

const onDragOver = () => {
  isDragOver.value = true;
};

const onDragLeave = () => {
  isDragOver.value = false;
};

const onDrop = (event) => {
  isDragOver.value = false;
  const files = event.dataTransfer.files;
  if (files && files.length > 0) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.type.includes('image')) {
        form.value.images.push(file);
      }
    }
  }
};

const imageUrls = computed(() => {
  return form.value.images.map((file) => URL.createObjectURL(file));
});

const removeImage = (event, index) => {
  event.stopPropagation();
  form.value.images.splice(index, 1);
};

const onSubmit = () => {
  console.log('Form submitted:', form.value, 'Tab:', form.value.activeTab);
};
</script>

<style scoped lang="scss">

// Hide tab arrows for cleaner look
:deep(.q-tabs--horizontal .q-tabs__arrow--left),
:deep(.q-tabs--horizontal .q-tabs__arrow--right) {
  display: none !important;
}

.sports-select {
  background-color: #ffedd5 !important;
  border: 1px solid #fed7aa !important;
  box-shadow: none !important;
  border-radius: 28px !important;

  :deep(.q-field__control),
  :deep(.q-field__native) {
    height: 34px !important;
    min-height: 34px !important;
    border-radius: 28px !important;
    &:before {
      border: none !important;
    }
  }

  :deep(.q-field__marginal) {
    height: 32px !important;
  }
}

.upload-container {
  border-radius: 8px;
  height: 150px;
  width: 100%;
  border: 2px dashed #e0e0e0;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  &:hover,
  &.drag-over {
    border-color: var(--q-primary);
    background-color: rgba(var(--q-primary), 0.05);
  }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 150px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  position: absolute;
  height: 100%;
  width: 100%;
  inset: 0px;
  border-radius: 8px;
  color: transparent;
  vertical-align: middle;
  object-fit: cover;
}

.close-btn {
  position: absolute;
  inset: 0px;
  top: 0px;
  right: 0px;
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  z-index: 1;
  opacity: 0;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.2s;
  &:hover {
    opacity: 1;
  }
}

// Mobile Responsive Styles - Native App Optimized
@media (max-width: 768px) {
  .event-type-section {
    .event-type-scroll {
      gap: 6px;
      padding: 2px 0 6px 0;

      .event-type-tag {
        font-size: 13px;
        padding: 6px 14px;
        border-radius: 18px;
      }
    }
  }
}

@media (max-width: 480px) {
  .event-type-section {
    .event-type-scroll {
      gap: 6px;
      padding: 2px 0 6px 0;

      .event-type-tag {
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 16px;
        min-height: 32px;
      }
    }
  }
}
</style>
