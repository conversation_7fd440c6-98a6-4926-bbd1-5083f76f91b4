# Development files
.env.local
.env.development
.env.test

# Build artifacts (except dist/spa which is our output)
dist/pwa
dist/ssr
dist/electron
dist/capacitor
dist/cordova

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
.tmp/
.temp/

# Cache directories
.cache/
.parcel-cache/

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories that should be installed fresh
# (commented out to let Vercel handle)
# node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Quasar development files
.quasar/dev
.quasar/build
