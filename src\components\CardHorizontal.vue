<template>
  <BaseCard @click="$emit('click')">
    <div class="row no-wrap items-center">
      <!-- Image Section -->
      <div class="col-auto">
        <slot name="image">
          <q-img
            :src="imageUrl"
            :alt="imageAlt"
            class="card-image"
            loading="lazy"
          >
            <slot name="badges" />
          </q-img>
        </slot>
      </div>
      
      <!-- Content Section -->
      <div class="col q-pa-sm">
        <slot name="header" />
        <slot name="price" />
        <slot name="meta" />
        <slot name="actions" />
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import BaseCard from './BaseCard.vue';

interface Props {
  imageUrl?: string;
  imageAlt?: string;
}

withDefaults(defineProps<Props>(), {
  imageUrl: '/images/image_default.jpg',
  imageAlt: 'Card image'
});

defineEmits<{
  click: [];
}>();
</script>

<style lang="scss" scoped>
.card-image {
  width: 90px;
  height: 90px;
  border-radius: 12px;
  margin: 12px;
  position: relative;
}

@media (max-width: 599px) {
  .card-image {
    width: 80px;
    height: 80px;
    margin: 10px;
  }
}
</style>
