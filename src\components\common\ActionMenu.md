# ActionMenu Component

A reusable dropdown menu component for common actions like share and report functionality.

## Features

- ✅ **Configurable button appearance** (size, icon, colors)
- ✅ **Flexible menu positioning** (anchor and self positioning)
- ✅ **Optional actions** (show/hide share and report)
- ✅ **Custom labels** for internationalization
- ✅ **Extensible** with custom actions via slots
- ✅ **Multiple styling variants** (dark, light, transparent)
- ✅ **TypeScript support** with proper type definitions

## Usage Examples

### 1. Basic Usage (EventCard)
```vue
<ActionMenu
  :item-id="item.id"
  @share="handleShare"
  @report="handleReport"
/>
```

### 2. Horizontal Card Usage (EventCardHorizontal)
```vue
<ActionMenu
  :item-id="item.id"
  size="xs"
  icon-color="grey-5"
  button-class="menu-btn--transparent"
  @share="handleShare"
  @report="handleReport"
/>
```

### 3. Custom Actions with Slot
```vue
<ActionMenu :item-id="item.id">
  <template #actions>
    <q-item clickable @click="customAction">
      <q-item-section avatar>
        <q-icon name="edit" color="blue-5" size="18px" />
      </q-item-section>
      <q-item-section>
        <q-item-label class="text-body2">Edit</q-item-label>
      </q-item-section>
    </q-item>
  </template>
</ActionMenu>
```

### 4. Only Report Option
```vue
<ActionMenu
  :item-id="item.id"
  :show-share="false"
  @report="handleReport"
/>
```

### 5. Light Theme Variant
```vue
<ActionMenu
  :item-id="item.id"
  icon-color="grey-7"
  button-class="menu-btn--light"
  @share="handleShare"
  @report="handleReport"
/>
```

### 6. Full Width Button
```vue
<ActionMenu
  :item-id="item.id"
  :full-width="true"
  icon="more_horiz"
  share-label="Share Event"
  report-label="Report Event"
  @share="handleShare"
  @report="handleReport"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | `string` | `'sm'` | Button size |
| `buttonClass` | `string` | `'menu-btn'` | CSS class for button |
| `icon` | `string` | `'more_vert'` | Icon name |
| `iconColor` | `string` | `'white'` | Icon color |
| `iconSize` | `string` | `'16px'` | Icon size |
| `fullWidth` | `boolean` | `false` | Make button full width |
| `anchor` | `string` | `'bottom right'` | Menu anchor position |
| `self` | `string` | `'top right'` | Menu self position |
| `menuWidth` | `string` | `'150px'` | Menu minimum width |
| `showShare` | `boolean` | `true` | Show share option |
| `showReport` | `boolean` | `true` | Show report option |
| `shareLabel` | `string` | `'Chia sẻ'` | Share button label |
| `reportLabel` | `string` | `'Báo cáo'` | Report button label |
| `itemId` | `string \| number` | `undefined` | Item ID for actions |
| `itemType` | `string` | `'event'` | Item type |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `share` | `itemId?: string \| number` | Emitted when share is clicked |
| `report` | `itemId?: string \| number` | Emitted when report is clicked |

## Slots

| Slot | Description |
|------|-------------|
| `actions` | Custom menu items |

## CSS Classes

| Class | Description |
|-------|-------------|
| `.menu-btn` | Default dark button style |
| `.menu-btn--light` | Light button variant |
| `.menu-btn--transparent` | Transparent button variant |

## Integration

The component is already integrated into:
- `EventCard.vue` - For card view events
- `EventCardHorizontal.vue` - For list view events

Both components emit the appropriate events that are handled by the parent `EventDiscovery.vue` component.
