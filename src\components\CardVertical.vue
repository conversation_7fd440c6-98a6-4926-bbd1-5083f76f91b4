<template>
  <BaseCard @click="$emit('click')">
    <!-- Image Section -->
    <div class="image-container">
      <slot name="image">
        <q-img
          :src="imageUrl"
          :alt="imageAlt"
          class="card-image"
          loading="lazy"
        >
          <slot name="badges" />
          <slot name="favorite" />
        </q-img>
      </slot>
    </div>
    
    <!-- Content Section -->
    <div class="content-section q-pa-md">
      <slot name="header" />
      <slot name="price" />
      <slot name="meta" />
      <slot name="actions" />
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import BaseCard from './BaseCard.vue';

interface Props {
  imageUrl?: string;
  imageAlt?: string;
}

withDefaults(defineProps<Props>(), {
  imageUrl: '/images/image_default.jpg',
  imageAlt: 'Card image'
});

defineEmits<{
  click: [];
}>();
</script>

<style lang="scss" scoped>
.image-container {
  position: relative;
  
  .card-image {
    width: 100%;
    height: 180px;
    position: relative;
  }
}

.content-section {
  flex: 1;
}

@media (max-width: 599px) {
  .image-container .card-image {
    height: 160px;
  }
}
</style>
