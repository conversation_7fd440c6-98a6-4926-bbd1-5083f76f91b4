import { useQuasar } from 'quasar';
import { eventDiscoveryQuickTags, eventsDiscovery } from 'src/pages/events/data/mock-data';
import { computed, ref } from 'vue';

export interface Participant {
  id: number;
  name: string;
  avatar: string;
  phone: string;
}

export interface Event {
  id: string | number;
  title: string;
  description: string;
  category: string;
  categoryId: string;
  skillLevels: string[];
  participants: Participant[];
  totalParticipants: number;
  ticketsSold?: number;
  totalTickets?: number;
  maxParticipants: number;
  timeSlots?: string[];
  startTime: string;
  endTime: string;
  date: string;
  location: string;
  price: number;
  photoUrl: string;
}

export function useDiscoveryEvent(searchQuery = ref('')) {
  const $q = useQuasar();
  const events = ref([...eventsDiscovery]);
  const selectedQuickTags = ref<string[]>(['beginner']);
  const maxParticipants = 3;

  const participantsDialog = ref(false);
  const currentEvent = ref<{
    participants: Participant[];
    totalParticipants: number;
    title: string;
  }>({
    participants: [],
    totalParticipants: 0,
    title: '',
  });

  const categoryFilters: Record<string, (event: Event) => boolean> = {
    today: (event) => event.date === new Date().toLocaleDateString('vi-VN'),
    nearby: (event) => event.location.includes('Sân PVB - Sân 1'),
    beginner: (event) => event.skillLevels.includes('beginner'),
    intermediate: (event) => event.skillLevels.includes('intermediate'),
    advanced: (event) => event.skillLevels.includes('advanced'),
  };

  const filteredEvents = computed(() => {
    let result = events.value;

    const query = searchQuery.value.trim().toLowerCase();
    if (query) {
      result = result.filter((event) =>
        [event.title, event.description, event.location].some((field) =>
          field.toLowerCase().includes(query),
        ),
      );
    }

    if (selectedQuickTags.value.length) {
      result = result.filter((event) =>
        selectedQuickTags.value.some((tag) =>
          categoryFilters[tag] ? categoryFilters[tag](event) : event.categoryId === tag,
        ),
      );
    }

    return result;
  });

  const formatParticipantNames = (participants: Participant[]): string => {
    const names = participants.map((p) => p.name);
    return names.length <= 2
      ? names.join(', ')
      : `${names[0]}, ${names[1]} và ${names.length - 2} người khác`;
  };

  const toggleCategory = (categoryId: string) => {
    const tags = selectedQuickTags.value;
    const idx = tags.indexOf(categoryId);
    if (idx > -1) {
      tags.splice(idx, 1);
    } else {
      tags.push(categoryId);
    }
  };

  const showParticipantsDialog = (event: Event): void => {
    currentEvent.value = {
      participants: event.participants,
      totalParticipants: event.totalParticipants,
      title: event.title,
    };
    participantsDialog.value = true;
  };

  const viewEvent = (eventId: string | number) => {
    $q.notify({
      message: `Xem chi tiết sự kiện ${eventId}`,
      color: 'primary',
    });
  };

  return {
    // data
    events: events.value,
    filteredEvents,
    selectedQuickTags,
    eventDiscoveryQuickTags,
    maxParticipants,

    // state
    participantsDialog,
    currentEvent,

    // methods
    toggleCategory,
    formatParticipantNames,
    showParticipantsDialog,
    viewEvent,
  };
}
