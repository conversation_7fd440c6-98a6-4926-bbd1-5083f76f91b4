<template>
  <q-dialog v-model="isOpen" persistent :maximized="$q.screen.lt.sm">
    <q-card class="column no-wrap" style="width: 100%; max-width: 690px;">
      <!-- Sticky Header -->
      <q-card-section class="row items-center q-pb-sm bg-white shadow-1 sticky-header">
        <div class="column">
          <div class="text-weight-bold text-grey-9" :class="$q.screen.lt.sm ? 'text-body1' : 'text-h6'">Tham gia</div>
          <div class="text-body2 text-grey-6 ellipsis">{{ eventData.title }}</div>
        </div>
        <q-space />
        <q-btn
          icon="close"
          flat
          round
          dense
          color="grey-7"
          size="md"
          @click="closeDialog"
        />
      </q-card-section>
      <q-separator />

      <!-- Scrollable Content -->
      <q-card-section class="col scroll q-pt-md q-pb-none scrollable-content">
        <!-- Skill Level Section -->
        <div class="q-mb-md">
          <div class="text-subtitle1 q-mb-xs text-grey-9 flex items-center">
            Tr<PERSON><PERSON> độ <span class="text-negative">*</span>
            <q-icon name="info" size="16px" color="grey-7" class="q-ml-sm">
              <q-tooltip class="text-body2" :offset="[0, 10]" anchor="bottom end" self="bottom end">
                Trình độ của bạn cho sự kiện này (ví dụ: Beginner, Intermediate, Advanced)
              </q-tooltip>
            </q-icon>
          </div>

          <div class="row items-center q-gutter-sm q-mb-sm">
            <q-chip
              v-for="level in skillLevels"
              :key="level.value"
              clickable
              size="md"
              :color="form.skillLevels.includes(level.value) ? 'primary' : 'grey-3'"
              :text-color="form.skillLevels.includes(level.value) ? 'white' : 'grey-8'"
              class="q-px-md q-py-sm"
              icon="tag"
              @click="toggleSkillLevel(level.value)"
            >
              {{ level.label }}
            </q-chip>
          </div>

          <div v-if="showSkillLevelError" class="text-negative text-caption q-mt-xs">
            Vui lòng chọn ít nhất một trình độ
          </div>
        </div>

        <!-- Number of Slots Section -->
        <div>
          <div class="text-subtitle1 q-mb-xs text-grey-9 flex items-center">
            Số lượng thành viên đăng ký <span class="text-negative">*</span>
            <q-icon name="info" size="16px" color="grey-7" class="q-ml-sm">
              <q-tooltip class="text-body2" :offset="[0, 10]" anchor="bottom end" self="bottom end">
                <div class="text-body2">Nhập số lượng người tham gia bạn muốn đăng ký</div>
              </q-tooltip>
            </q-icon>
          </div>
          
          <q-input
            v-model.number="form.numberOfSlots"
            type="number"
            outlined
            placeholder="0"
            input-class="ext-h6 text-weight-bold"
            min="1"
            max="10"
            :rules="[(val) => !!val || 'Số lượng chỗ là bắt buộc']"
          >
            <template v-slot:prepend>
              <q-btn
                icon="remove"
                :color="!form.numberOfSlots || form.numberOfSlots <= 1 ? 'grey-6' : 'primary'"
                class="q-mr-sm"
                flat
                round
                outlined
                @click="decrementSlots"
                :disable="!form.numberOfSlots || form.numberOfSlots <= 1"
              />
            </template>
            <template v-slot:append>
              <q-btn
                flat
                icon="add"
                :color="!form.numberOfSlots || form.numberOfSlots >= 10 ? 'grey-6' : 'primary'"
                round
                outlined
                class="q-ml-sm"
                @click="incrementSlots"
                :disable="!form.numberOfSlots || form.numberOfSlots >= 10"
              />
            </template>
          </q-input>
        </div>

        <!-- Phone Number Section -->
        <div class="q-mb-md">
          <div class="text-subtitle1 q-mb-xs text-grey-9">
            Số điện thoại <span class="text-negative">*</span>
          </div>
          <PhoneInput
            v-model="form.phoneNumber"
            :mask="'+84'"
            class="full-width"
            label="Số điện thoại"
            @update:isValid="isPhoneValid = $event"
            ref="phoneInputRef"
          />
        </div>

        <!-- Event Card Section -->
        <q-card class="rounded-borders shadow-2 q-mb-lg">
          <!-- Event Image with Overlay -->
          <div class="relative-position" style="height: 160px;">
            <q-img
              :src="eventData.photoUrl || '/images/image_default.jpg'"
              class="full-width full-height"
              style="border-radius: 16px;"
              fit="cover"
              loading="lazy"
            >
              <!-- Gradient Overlay -->
              <div class="absolute-full bg-gradient-to-t from-black-5 to-transparent"></div>
              <!-- Event Title -->
              <div class="absolute-bottom q-pa-md">
                <div class="text-white text-weight-bold text-h6 text-shadow">
                  {{ eventData.title }}
                </div>
              </div>
            </q-img>
          </div>
          <!-- Event Details -->
          <q-card-section class="q-pa-md">
            <!-- Date -->
            <div class="row items-start q-mb-md">
              <Calendar color="#ff8142" :size="20" class="q-mr-sm q-mt-xs" />
              <div class="column">
                <div class="text-body2 text-grey-6">Ngày</div>
                <div class="text-subtitle1 text-weight-medium text-grey-9">{{ eventData.date }}</div>
              </div>
            </div>

            <!-- Time -->
            <div class="row items-start q-mb-md">
              <Clock color="#ff8142" :size="20" class="q-mr-sm q-mt-xs" />
              <div class="column">
                <div class="text-body2 text-grey-6">Giờ</div>
                <div class="text-subtitle1 text-weight-medium text-grey-9">
                  {{ eventData.startTime }} - {{ eventData.endTime }}
                </div>
              </div>
            </div>

            <!-- Host -->
            <div class="row items-start q-mb-md">
              <User color="#ff8142" :size="20" class="q-mr-sm q-mt-xs" />
              <div class="column">
                <div class="text-body2 text-grey-6">Host</div>
                <div class="text-subtitle1 text-weight-medium text-grey-9 q-mb-xs">
                  {{ eventData.organizer.name }}
                </div>
                <div class="row items-center">
                  <Phone color="#ff8142" :size="16" class="q-mr-xs" />
                  <a
                    :href="`tel:${eventData.organizer.phoneNumber}`"
                    class="text-body2 text-weight-medium text-primary no-decoration"
                  >
                    {{ eventData.organizer.phoneNumber }}
                  </a>
                </div>
              </div>
            </div>

            <!-- Price -->
            <q-separator class="q-my-md" />
            <div class="row justify-between items-center">
              <div class="text-subtitle2 text-grey-8 row items-center">
                <CreditCard color="#ff8142" :size="20" class="q-mr-sm" />
                <span>Phí tham gia:</span>
              </div>
              <div class="text-h6 text-primary text-weight-bold">${{ eventData.price }}/người</div>
            </div>
          </q-card-section>
        </q-card>
      </q-card-section>

      <!-- Sticky Footer -->
      <q-separator />
      <q-card-actions class="q-pa-md bg-white shadow-up-1 sticky-footer">
        <q-btn
          color="primary"
          :disable="
            !form.skillLevels.length || !form.numberOfSlots || !form.phoneNumber || !isPhoneValid
          "
          size="lg"
          label="Tiếp tục"
          class="full-width q-py-sm text-weight-bold"
          rounded
          unelevated
          @click="confirmJoin"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { Calendar, Clock, CreditCard, Phone, User } from 'lucide-vue-next';
import PhoneInput from 'src/components/common/PhoneInput.vue';
import type { EventJoinData } from 'src/composables/events/useEventJoinFlow';
import { computed, ref, watch } from 'vue';
import { useUserStore } from 'src/stores/userStore';
import { useQuasar } from 'quasar';
const userStore = useUserStore();

const $q = useQuasar();

// Default event data
const defaultEvent = {
  id: '',
  title: 'Acoustic Serenade Showcase',
  maxSlots: 10,
  skillLevels: ['beginner', 'intermediate', 'advanced'],
  date: 'Thứ Hai, 09/06/2025',
  startTime: '10:00 PM',
  endTime: '01:00 AM',
  price: '80K',
  organizer: {
    name: 'Nguyễn Văn A',
    phoneNumber: '+1 (212) 2222222',
  },
  photoUrl: '',
};

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  event: {
    type: Object as () => EventJoinData | null,
    default: null,
  },
});

// Computed property to get event data with fallbacks to default values
const eventData = computed(() => {
  if (!props.event) return defaultEvent;

  return {
    id: props.event.id,
    title: props.event.title || defaultEvent.title,
    maxSlots: props.event.maxSlots || defaultEvent.maxSlots,
    skillLevels: defaultEvent.skillLevels,
    date: props.event.date || defaultEvent.date,
    startTime: props.event.startTime || defaultEvent.startTime,
    endTime: props.event.endTime || defaultEvent.endTime,
    price: props.event.price || defaultEvent.price,
    photoUrl: props.event.photoUrl || defaultEvent.photoUrl,
    organizer: {
      name: props.event.organizer?.name || defaultEvent.organizer.name,
      phoneNumber:
        props.event.organizer?.phoneNumber ||
        props.event.organizerPhoneNumber ||
        defaultEvent.organizer.phoneNumber,
    },
  };
});

const skillLevels = [
  { label: 'Beginner', value: 'beginner' },
  { label: 'Intermediate', value: 'intermediate' },
  { label: 'Advanced', value: 'advanced' },
];

const form = ref({
  skillLevels: [] as string[],
  numberOfSlots: null as number | null,
  phoneNumber: '',
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);
const showSkillLevelError = ref(false);
const isPhoneValid = ref(false);
const phoneInputRef = ref();
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

watch(isOpen, (val) => {
  if (val) {
    form.value = {
      skillLevels: [],
      numberOfSlots: null,
      phoneNumber: userStore.user?.phoneNumber || '',
    };
    showSkillLevelError.value = false;
    isPhoneValid.value = !!userStore.user?.phoneNumber;
  }
});

function toggleSkillLevel(level: string) {
  const index = form.value.skillLevels.indexOf(level);
  if (index === -1) {
    form.value.skillLevels.push(level);
  } else {
    form.value.skillLevels.splice(index, 1);
  }
}

const incrementSlots = () => {
  if (form.value.numberOfSlots === null) form.value.numberOfSlots = 1;
  else if (form.value.numberOfSlots < 10) form.value.numberOfSlots++;
};

const decrementSlots = () => {
  if (form.value.numberOfSlots && form.value.numberOfSlots > 1) {
    form.value.numberOfSlots--;
  }
};

const closeDialog = () => {
  isOpen.value = false;
  emit('cancel');
};

const confirmJoin = () => {
  if (form.value.skillLevels.length === 0) {
    showSkillLevelError.value = true;
    return;
  }
  showSkillLevelError.value = false;
  isOpen.value = false;
  emit('confirm', {
    skillLevels: form.value.skillLevels,
    numberOfSlots: form.value.numberOfSlots,
    phoneNumber: form.value.phoneNumber,
    organizerPhoneNumber: eventData.value.organizer.phoneNumber,
    eventId: eventData.value.id,
  });
};
</script>

<style scoped>
/* Sticky Header and Footer */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.sticky-footer {
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.scrollable-content {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

/* Custom gradient utilities using CSS variables for Quasar compatibility */
.bg-gradient-to-t {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
}

.from-black-5 {
  --tw-gradient-from: rgba(0, 0, 0, 0.5);
}

.to-transparent {
  --tw-gradient-to: transparent;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.shadow-up-1 {
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.no-decoration {
  text-decoration: none;
}

/* Mobile-first responsive adjustments */
@media (max-width: 599px) {
  .q-dialog__inner {
    padding: 0;
  }
}
</style>
