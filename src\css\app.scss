// app global css in SCSS form
body {
  font-size: 16px;
  /* Default browser size */
  font-family:
    -apple-system, BlinkMacSystemFont, 'Roboto', 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}

// Card styling with rounded corners
.q-card {
  box-shadow:
    rgba(0, 0, 0, 0) 0px 0px 0px 0px,
    rgba(0, 0, 0, 0) 0px 0px 0px 0px,
    rgba(0, 0, 0, 0.05) 0px 1px 2px 0px !important;
  border: 1px solid rgb(243, 244, 246) !important;
  border-radius: 16px !important;
}

// Card with no border radius
.card-square {
  border-radius: 0 !important;
}

// Alternative approach - more specific selectors
.q-btn .q-btn__content {
  text-transform: none !important;
}

// Ensure it works for all button variants
.q-btn--standard,
.q-btn--outline,
.q-btn--flat,
.q-btn--unelevated,
.q-btn--push {
  text-transform: none !important;
  box-shadow: none !important;
  border-radius: 12px !important;
  .q-btn__content {
    text-transform: none !important;
  }

  // Remove shadows from all button states
  &:hover,
  &:focus,
  &:active,
  &.q-btn--active {
    box-shadow: none !important;
  }
}

// Remove shadows from all button states globally
.q-btn:hover,
.q-btn:focus,
.q-btn:active,
.q-btn.q-btn--active {
  box-shadow: none !important;
}

.q-btn:before {
  box-shadow: none !important;
}

// Dark mode placeholder
.body--dark .q-field--outlined input::placeholder,
.body--dark .q-field--outlined textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.q-btn-group {
  box-shadow: $shadow-1 !important;
}

.country-selector[data-v-513fd7a5] {
  border-radius: 0 !important;
}

.q-field--outlined {
  .q-field__control {
    border-radius: 8px !important;
  }
}

// .q-field__control {
//   height: 52px !important;
// }

// Floating label modifications
// .q-field--float {
//   .q-field__label {
//     transform: translateY(-50%) !important;
//     top: 0 !important;
//     background: var(--q-white) !important;
//     padding: 0 4px !important;
//     margin-left: -4px !important;
//     z-index: 1 !important;
//   }
// }

// .q-field--float .q-field__label,
// .q-field--float .q-field__label:before,
// .q-field--float .q-field__label:after {
//   background-color: #ffffff !important;
//   font-size: 14px !important;
// }

// .q-field--labeled .q-field__native,
// .q-field--labeled .q-field__prefix,
// .q-field--labeled .q-field__suffix {
//   margin-bottom: 10px !important;
//   padding-bottom: 14px !important;
//   font-size: 14px !important;
// }

// .q-field--dense .q-field__control,
// .q-field--dense .q-field__marginal {
//   height: 52px !important;
// }

a {
  color: $primary !important; // or any color you want
  text-decoration: none !important; // or none, if you don't want underline
  cursor: pointer !important;
}

.q-select__dialog:has(.q-select-dialog-content) {
  width: 690px !important;
  margin: 0 auto !important;
  max-width: 690px !important;
}
