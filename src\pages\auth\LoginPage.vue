<template>
  <q-page class="column items-center justify-center q-pa-md">
    <q-card-section class="text-center q-pb-none">
      <div class="text-h4 text-weight-bold text-dark q-mb-sm">{{ t('authentication.signIn') }}</div>
      <p class="text-body2 text-grey-6">{{ t('authentication.description') }}</p>
    </q-card-section>
    <q-card-section class="full-width">
      <q-form @submit="handleEmailLogin" class="q-gutter-y-md">
        <q-input
          v-model="email"
          type="email"
          outlined
          :label="t('authentication.email')"
          :rules="[(val) => !!val || t('authentication.enterEmail')]"
        />

        <q-input
          v-model="password"
          :type="showPassword ? 'text' : 'password'"
          outlined
          rounded
          autocomplete="off"
          :label="t('authentication.password')"
          :rules="[(val) => !!val || t('authentication.enterPassword')]"
        >
          <template v-slot:append>
            <q-icon
              :name="showPassword ? 'visibility_off' : 'visibility'"
              class="cursor-pointer"
              @click="showPassword = !showPassword"
            />
          </template>
        </q-input>

        <div class="row items-center justify-end q-mb-md">
          <router-link to="#">{{ t('authentication.forgotPassword') }}</router-link>
        </div>

        <q-btn
          type="submit"
          :loading="localLoading"
          :disable="localLoading"
          color="primary"
          class="full-width"
          size="lg"
          >{{ t('authentication.signIn') }}</q-btn
        >

        <div class="social-login-container q-mb-md">
          <!-- Divider -->
          <q-separator class="q-my-lg" />
          <div class="text-center text-grey-6 q-mb-md">{{ t('authentication.or') }}</div>
          <!-- Social Login Buttons -->
          <div class="row justify-center q-gutter-md">
            <q-btn
              round
              :loading="localLoading"
              color="grey-2"
              size="lg"
              @click="handleGoogleLogin"
              class="social-btn"
            >
              <GoogleIcon />
            </q-btn>
          </div>
        </div>

        <p class="text-center q-ma-none">
          {{ t('authentication.dontHaveAccount') }}
          <router-link to="/auth/signup">{{ t('authentication.signUp') }}</router-link>
        </p>
      </q-form>
    </q-card-section>
    <q-dialog v-model="showError" persistent>
      <q-card>
        <q-card-section>
          <div class="text-h6">Error</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          {{ auth.error?.message }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="OK" color="primary" v-close-popup @click="auth.error = null" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import GoogleIcon from 'src/components/Icons/GoogleIcon.vue';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { useAuthStore } from 'src/stores/authStore';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const auth = useAuthStore();
const $q = useQuasar();
const router = useRouter();

// Form state
const email = ref('');
const password = ref('');
const showPassword = ref(false);
const localLoading = ref(false);

// UI state
const error = computed(() => auth.error);
const showError = computed({
  get: () => !!error.value,
  set: () => (auth.error = null),
});

// Handle Google login
const handleGoogleLogin = async () => {
  try {
    localLoading.value = true;

    await auth.loginWithGoogle();
    const profileRef = localStorage.getItem(LOCAL_STORAGE_KEYS.PROFILE_SETUP);
    const profile = JSON.parse(profileRef || '{}');

    if (profile?.phoneVerified) {
      router.replace('/');
    } else {
      router.replace('/onboarding');
    }
  } catch (err: any) {
    $q.notify({
      type: 'negative',
      message: err.message || 'Failed to login with Google',
    });
  } finally {
    localLoading.value = false;
  }
};

// Handle email/password login
const handleEmailLogin = async () => {
  try {
    localLoading.value = true;
    await auth.loginWithEmail(email.value, password.value);
    const profileRef = localStorage.getItem(LOCAL_STORAGE_KEYS.PROFILE_SETUP);
    const profile = JSON.parse(profileRef || '{}');

    if (profile?.phoneVerified) {
      router.replace('/');
    } else {
      router.replace('/onboarding');
    }
  } catch (err: any) {
    console.error('Email login failed:', err);
    $q.notify({
      type: 'negative',
      message: err.message || 'Failed to login with email',
    });
  } finally {
    localLoading.value = false;
  }
};
</script>
