<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">{{ t('navigation.profile') }}</div>
    <div class="text-subtitle1 q-mb-lg text-grey-7">Qu<PERSON><PERSON> lý thông tin cá nhân</div>

    <!-- Profile Header -->
    <q-card class="profile-header q-mb-lg">
      <q-card-section class="text-center">
        <q-avatar size="80px" class="q-mb-md">
          <img :src="userProfile.avatar" alt="Avatar" />
        </q-avatar>

        <div class="text-h6">{{ userProfile.displayName }}</div>
        <div class="text-body2 text-grey-6">{{ userProfile.email }}</div>

        <div class="row justify-center q-gutter-sm q-mt-md">
          <q-chip
            v-if="userProfile.phoneVerified"
            color="positive"
            text-color="white"
            icon="verified"
            size="sm"
          >
            <PERSON><PERSON> x<PERSON><PERSON> thực
          </q-chip>

          <q-chip color="primary" text-color="white" icon="star" size="sm">
            {{ userProfile.membershipLevel }}
          </q-chip>
        </div>
      </q-card-section>
    </q-card>

    <!-- Quick Stats -->
    <div class="row q-col-gutter-md q-mb-lg">
      <div class="col-4">
        <q-card class="text-center">
          <q-card-section>
            <div class="text-h5 text-primary">{{ userStats.totalBookings }}</div>
            <div class="text-caption text-grey-6">Tổng đặt chỗ</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-4">
        <q-card class="text-center">
          <q-card-section>
            <div class="text-h5 text-positive">{{ userStats.completedBookings }}</div>
            <div class="text-caption text-grey-6">Đã hoàn thành</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-4">
        <q-card class="text-center">
          <q-card-section>
            <div class="text-h5 text-orange">{{ userStats.favoriteVenues }}</div>
            <div class="text-caption text-grey-6">Sân yêu thích</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Menu Options -->
    <q-list bordered separator class="rounded-borders">
      <!-- Personal Information -->
      <q-item clickable v-ripple @click="editProfile">
        <q-item-section avatar>
          <q-icon name="person" color="primary" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Thông tin cá nhân</q-item-label>
          <q-item-label caption>Chỉnh sửa thông tin cá nhân</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Sports Preferences -->
      <q-item clickable v-ripple @click="manageSports">
        <q-item-section avatar>
          <q-icon name="sports" color="orange" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Môn thể thao yêu thích</q-item-label>
          <q-item-label caption>{{ userProfile.favoriteSports.join(', ') }}</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Payment Methods -->
      <q-item clickable v-ripple @click="managePayments">
        <q-item-section avatar>
          <q-icon name="payment" color="green" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Phương thức thanh toán</q-item-label>
          <q-item-label caption>Quản lý thẻ và ví điện tử</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Notifications -->
      <q-item clickable v-ripple @click="manageNotifications">
        <q-item-section avatar>
          <q-icon name="notifications" color="blue" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Thông báo</q-item-label>
          <q-item-label caption>Cài đặt thông báo</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Language -->
      <q-item clickable v-ripple @click="changeLanguage">
        <q-item-section avatar>
          <q-icon name="language" color="purple" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Ngôn ngữ</q-item-label>
          <q-item-label caption>Tiếng Việt</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Help & Support -->
      <q-item clickable v-ripple @click="getSupport">
        <q-item-section avatar>
          <q-icon name="help" color="teal" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Hỗ trợ</q-item-label>
          <q-item-label caption>Trung tâm trợ giúp</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Privacy Policy -->
      <q-item clickable v-ripple @click="viewPrivacy">
        <q-item-section avatar>
          <q-icon name="privacy_tip" color="indigo" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Chính sách bảo mật</q-item-label>
          <q-item-label caption>Điều khoản và chính sách</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>

      <!-- Logout -->
      <q-item clickable v-ripple @click="logout">
        <q-item-section avatar>
          <q-icon name="logout" color="negative" />
        </q-item-section>
        <q-item-section>
          <q-item-label>Đăng xuất</q-item-label>
          <q-item-label caption>Thoát khỏi tài khoản</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="chevron_right" color="grey" />
        </q-item-section>
      </q-item>
    </q-list>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { useAuthStore } from 'src/stores/authStore';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const $q = useQuasar();
const router = useRouter();
const authStore = useAuthStore();

// User profile data
const userProfile = ref({
  displayName: 'Nguyễn Văn A',
  email: '<EMAIL>',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
  phoneVerified: true,
  membershipLevel: 'Gold',
  favoriteSports: ['Bóng đá', 'Tennis', 'Cầu lông'],
});

// User statistics
const userStats = ref({
  totalBookings: 24,
  completedBookings: 18,
  favoriteVenues: 5,
});

// Methods
const editProfile = () => {
  $q.notify({
    message: 'Chỉnh sửa thông tin cá nhân',
    color: 'primary',
  });
};

const manageSports = () => {
  $q.notify({
    message: 'Quản lý môn thể thao yêu thích',
    color: 'orange',
  });
};

const managePayments = () => {
  $q.notify({
    message: 'Quản lý phương thức thanh toán',
    color: 'green',
  });
};

const manageNotifications = () => {
  $q.notify({
    message: 'Cài đặt thông báo',
    color: 'blue',
  });
};

const changeLanguage = () => {
  $q.notify({
    message: 'Thay đổi ngôn ngữ',
    color: 'purple',
  });
};

const getSupport = () => {
  $q.notify({
    message: 'Trung tâm trợ giúp',
    color: 'teal',
  });
};

const viewPrivacy = () => {
  $q.notify({
    message: 'Chính sách bảo mật',
    color: 'indigo',
  });
};

const logout = () => {
  $q.dialog({
    title: 'Xác nhận đăng xuất',
    message: 'Bạn có chắc chắn muốn đăng xuất không?',
    cancel: true,
    persistent: true,
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
  }).onOk(async () => {
    try {
      await authStore.clearAuth();
      $q.notify({
        message: 'Đã đăng xuất thành công',
        color: 'positive',
      });
      router.push('/auth/login');
    } catch {
      $q.notify({
        message: 'Có lỗi xảy ra khi đăng xuất',
        color: 'negative',
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.q-item {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}
</style>
