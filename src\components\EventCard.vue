<template>
  <q-card class="event-card" v-ripple="$q.screen.lt.sm">
    <q-img
      :src="item.photoUrl ? item.photoUrl : '/images/image_default.jpg'"
      fit="cover"
      class="full-width"
      :style="$q.screen.lt.sm ? 'height: 150px' : 'height: 180px'"
      :loading-show="true"
    >
      <!-- Badge -->
      <q-chip
        class="absolute-top-left text-white q-ma-sm"
        size="16px"
        square
        style="background-color: rgba(0, 0, 0, 0.7);"
      >
        {{ item.price }} - {{ item.price }}
      </q-chip>
      <template v-slot:loading>
        <q-skeleton animation="wave" size="100%" type="rect" square />
      </template>
    </q-img>
    <div class="absolute-top-right q-mx-sm q-my-xs">
      <ActionMenu
        :item-id="item.id || undefined"
        @share="handleShareEvent"
        @report="handleReportEvent"
      />
    </div>
      <q-card-section class="justify-between">  <!-- Badge -->
        <q-chip
          v-for="skillLevel in item.skillLevels"
          square
          size="10px"
          color="grey-3"
          style="margin-top: -10px;"
          icon="tag"
          :key="skillLevel"
        >
          {{ skillLevel }}
        </q-chip>
      <!-- Event name -->
      <router-link :to="`/events/${item.id}`">
        <div class="text-subtitle1 text-bold cursor-pointer">
          {{ item.title }}
        </div>
      </router-link>
      <div class="text-body2 text-grey-8 q-mb-sm">{{ item.description }}</div>

      <!-- Event detail info - Ultra Compact Layout -->
      <div class="event-details-compact q-mt-sm">
        <!-- Single Row Layout -->
        <div class="row items-center justify-between q-mb-xs">
          <!-- Location -->
          <div class="row items-center q-gutter-x-xs flex-1">
            <MapPin :size="13" color="#ff8142" />
            <span class="text-body2 text-grey-7">{{ item.location }}</span>
          </div>
          <!-- Map Button -->
          <q-btn
            @click.stop
            flat
            round
            dense
            size="xs"
            color="primary"
            :href="`https://maps.google.com/?q=${item.location.split('•')[0]}`"
            target="_blank"
            class="location-btn"
          >
            <SquareArrowOutUpRight :size="11" />
          </q-btn>
        </div>
        <div class="row items-center justify-between">
          <!-- Participants -->
          <div class="row items-center q-gutter-x-xs">
            <CircleUser :size="13" color="#ff8142" />
            <span class="text-body2 text-grey-7">
              Slots còn lại: <span class="text-primary text-weight-bold">{{ item.maxParticipants ?? 7 }}</span>
            </span>
          </div>
        </div>
      </div>

      <div class="participants-section">
        <slot name="participants" />
      </div>

      <div class="event-host-section">
        <slot name="eventHost" />
      </div>
    </q-card-section>
    <q-separator />
    <q-card-actions class="q-pa-sm d-flex justify-between">
     <slot name="footerActions" />
    </q-card-actions>
  </q-card>
  <ShareModal :is-open="isShareModalOpen" :url="shareUrl" :onClose="closeShareModal" />
</template>

<script setup lang="ts">
import { SquareArrowOutUpRight, MapPin, CircleUser } from 'lucide-vue-next';
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import ShareModal from './common/ShareModal/ShareModal.vue';
import ActionMenu from './common/ActionMenu.vue';

const $q = useQuasar();

type EventCardProps = {
  item: {
    id?: string | number;
    title: string;
    location: string;
    description?: string;
    date: string;
    startTime: string;
    endTime: string;
    price: number;
    skillLevels?: string[];
    photoUrl?: string;
    maxParticipants?: number;
    locationMap?: {
      latitude: number;
      longitude: number;
    };
  };
};

const props = defineProps<EventCardProps>();
const emit = defineEmits<{
  report: [eventId: string | number];
}>();

const item = computed(() => props.item);
const isShareModalOpen = ref(false);
const shareUrl = computed(() => {
  const baseUrl = window.location.origin;
  const eventTitle = item.value.title.replace(/\s+/g, '-').toLowerCase();
  const eventId = item.value.id || eventTitle;
  return `${baseUrl}/events/${eventTitle}&eventId=${eventId}`;
});

const handleShareEvent = () => {
  isShareModalOpen.value = true;
};

const handleReportEvent = () => {
  if (item.value.id) {
    emit('report', item.value.id);
  }
};

const closeShareModal = () => {
  isShareModalOpen.value = false;
};

</script>

<style scoped lang="scss">
.event-card {
  border: 1px solid #f1f5f9;
  overflow: hidden;
  cursor: pointer;
  border-radius: 12px !important;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.q-img {
  border-top-left-radius: 12px !important;
  border-top-right-radius: 12px !important;
}

.upload-icon {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 100%;
  width: 2rem;
  height: 2rem;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.05);
  }
}



.venue-image {
  border-radius: 12px;
}

// Event details styling - Ultra Compact
.event-details-compact {
  .row {
    min-height: 20px;

    span {
      font-size: 13px;
      line-height: 1.3;
    }
  }
}

@media (max-width: 768px) {
  .event-title,
  .event-location,
  .event-time {
    max-width: 140px;
  }

  .event-details-compact {
    .location-text {
      font-size: 12px;
      max-width: calc(100% - 28px);
    }

    .location-btn {
      width: 18px;
      height: 18px;
      min-width: 18px;
    }

    .row {
      min-height: 18px;

      span {
        font-size: 12px;
      }
    }
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .event-card,
  .upload-icon {
    transition: none;
  }

  .event-card:hover {
    transform: none;
  }

  .upload-icon:hover {
    transform: none;
  }
}
</style>
