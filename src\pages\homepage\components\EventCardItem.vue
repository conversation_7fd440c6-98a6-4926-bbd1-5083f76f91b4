<template>
  <q-card class="discover-event-card">
    <div class="card-image">
      <q-img
        :src="event.photoUrl"
        :alt="event.title"
        class="event-image"
        loading="lazy"
      >
        <!-- Skill Level Badge -->
        <div v-if="event.skillLevels?.length" class="skill-badge">
          {{ event.skillLevels[0] }}
        </div>
        
        <!-- Action Menu -->
        <div class="action-menu-wrapper">
          <ActionMenu
            :item-id="event.id"
            size="sm"
            icon-size="14px"
            @share="handleShare"
            @report="handleReport"
          />
        </div>
      </q-img>
    </div>
    
    <q-card-section class="card-content">
      <!-- Event Title -->
      <router-link :to="`/events/${event.id}`">
        <div class="event-title cursor-pointer">{{ event.title }}</div>
      </router-link>
      
      <!-- Event Details -->
      <div class="event-details">
        <!-- Location -->
        <div class="detail-row">
          <MapPin :size="12" />
          <span class="detail-text">{{ event.location }}</span>
        </div>
        
        <!-- Date & Time -->
        <div class="detail-row">
          <Clock :size="12" />
          <span class="detail-text">{{ formatDateTime }}</span>
        </div>
        
        <!-- Available Slots -->
        <div v-if="event.maxParticipants" class="detail-row">
          <Users :size="12" />
          <span class="detail-text">{{ event.maxParticipants }} slots available</span>
        </div>
      </div>
      
      <!-- Footer: Price and Participants -->
      <div class="footer-section">
        <div v-if="event.price" class="price-info">
          <span class="price">${{ event.price }}</span>
          <span class="price-label">/Person</span>
        </div>

        <div v-if="event.participants?.length" class="participants-info">
          <div class="participant-avatars">
            <q-avatar
              v-for="(participant, index) in displayParticipants"
              :key="participant.id"
              size="24px"
              class="participant-avatar"
              :style="{ zIndex: displayParticipants.length - index }"
            >
              <img :src="participant.avatar" :alt="participant.name" />
            </q-avatar>
            <div v-if="remainingCount > 0" class="more-count">
              +{{ remainingCount }}
            </div>
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>

  <!-- Share Modal -->
  <ShareModal :is-open="isShareModalOpen" :url="shareUrl" :onClose="closeShareModal" />

  <!-- Report Dialog -->
  <q-dialog 
    v-model="reportDialog"
    :full-width="$q.screen.lt.sm"
  >
    <ReportDialog
      :target-id="reportTargetId"
      target-type="event"
      @close="reportDialog = false"
      @submit="handleReportSubmit"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { Clock, MapPin, Users } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import { useQuasar } from 'quasar';
import ActionMenu from 'src/components/common/ActionMenu.vue';
import ShareModal from 'src/components/common/ShareModal/ShareModal.vue';
import ReportDialog from 'src/components/common/ReportDialog.vue';

interface Participant {
  id: number;
  name: string;
  avatar: string;
}

interface DiscoverEvent {
  id: string;
  title: string;
  photoUrl: string;
  location: string;
  date: string;
  startTime: string;
  endTime: string;
  price?: number;
  skillLevels?: string[];
  maxParticipants?: number;
  participants?: Participant[];
}

interface Props {
  event: DiscoverEvent;
}

const props = defineProps<Props>();

defineEmits<{
  click: [];
  share: [event: DiscoverEvent];
  report: [eventId: string | number];
}>();

// Composables
const $q = useQuasar();

// Reactive state for dialogs
const isShareModalOpen = ref(false);
const reportDialog = ref(false);
const reportTargetId = ref<string | number>('');

// Share URL computation
const shareUrl = computed(() => {
  const baseUrl = window.location.origin;
  const eventTitle = props.event.title.replace(/\s+/g, '-').toLowerCase();
  const eventId = props.event.id || eventTitle;
  return `${baseUrl}/events/${eventTitle}&eventId=${eventId}`;
});

// Handler functions
const handleShare = () => {
  isShareModalOpen.value = true;
};

const handleReport = (eventId?: string | number) => {
  if (eventId) {
    reportTargetId.value = eventId;
    reportDialog.value = true;
  }
};

// Close share modal
const closeShareModal = () => {
  isShareModalOpen.value = false;
};

// Handle report submission
const handleReportSubmit = (reportData: any) => {
  console.log('Report submitted:', reportData);

  // Show success notification
  $q.notify({
    type: 'positive',
    message: 'Báo cáo đã được gửi thành công',
    position: 'top',
    timeout: 3000
  });

  reportDialog.value = false;
};

const formatDateTime = computed(() => {
  const date = new Date(props.event.date);
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  let dateStr = '';
  if (date.toDateString() === today.toDateString()) {
    dateStr = 'Today';
  } else if (date.toDateString() === tomorrow.toDateString()) {
    dateStr = 'Tomorrow';
  } else {
    dateStr = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }

  return `${dateStr} ${props.event.startTime} - ${props.event.endTime}`;
});

const displayParticipants = computed(() => {
  const participants = props.event.participants || [];
  return participants.slice(0, 3); // Show max 3 avatars
});

const remainingCount = computed(() => {
  const participants = props.event.participants || [];
  return Math.max(0, participants.length - 3);
});
</script>

<style lang="scss" scoped>
.discover-event-card {
  width: 220px;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .card-image {
    position: relative;
    height: 120px;
    
    .event-image {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
    
    .skill-badge {
      position: absolute;
      top: 8px;
      left: 8px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      backdrop-filter: blur(4px);
    }
    
    .action-menu-wrapper {
      position: absolute;
      top: -11px;
      right: -8px;

      :deep(.menu-btn) {
        background: rgba(0, 0, 0, 0.8) !important;
        color: white !important;
        width: 28px;
        height: 28px;
        backdrop-filter: blur(4px);

        &:hover {
          background: white !important;
          color: #ff8142 !important;
        }

        .q-icon {
          color: inherit !important;
        }
      }
    }
  }
  
  .card-content {
    padding: 12px;
    
    .event-title {
      font-size: 14px;
      font-weight: 600;
      color: #212529;
      margin-bottom: 8px;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .event-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-bottom: 8px;
      
      .detail-row {
        display: flex;
        align-items: center;
        gap: 6px;
        
        svg {
          color: #ff8142;
          flex-shrink: 0;
        }
        
        .detail-text {
          font-size: 11px;
          color: #6c757d;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    
    .footer-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 8px;
      border-top: 1px solid #f1f5f9;

      .price-info {
        display: flex;
        align-items: baseline;
        gap: 2px;

        .price {
          font-size: 16px;
          font-weight: 700;
          color: #ff8142;
        }

        .price-label {
          font-size: 10px;
          color: #9ca3af;
        }
      }

      .participants-info {
        .participant-avatars {
          display: flex;
          align-items: center;

          .participant-avatar {
            border: 2px solid white;
            margin-left: -6px;

            &:first-child {
              margin-left: 0;
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .more-count {
            background: #ff8142;
            color: white;
            font-size: 10px;
            font-weight: 600;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: -6px;
            border: 2px solid white;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .discover-event-card {
    min-width: 200px;
    width: 200px;
    
    .card-image {
      height: 100px;
    }
    
    .card-content {
      padding: 10px;
      
      .event-title {
        font-size: 13px;
      }
      
      .event-details {
        .detail-text {
          font-size: 10px;
        }
      }
      
      .footer-section {
        .price-info {
          .price {
            font-size: 14px;
          }

          .price-label {
            font-size: 9px;
          }
        }

        .participants-info {
          .participant-avatars {
            .participant-avatar {
              width: 20px;
              height: 20px;
            }

            .more-count {
              width: 20px;
              height: 20px;
              font-size: 9px;
            }
          }
        }
      }
    }
  }
}
</style>
