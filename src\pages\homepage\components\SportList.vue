<template>
  <!-- Sport Slide -->
  <div class="column justify-center">
    <div class="row items-center justify-between">
      <CardItem
        v-for="item in sportItems"
        :key="item.id"
        :cardProps="{ containerClass: 'card-sport column items-center justify-center' }"
      >
        <div class="column items-center">
          <div
            class="sport-icon-wrapper q-mb-sm"
            :class="{ 'sport-active': activeSportId === item.id }"
            @click="setActiveSport(item.id)"
          >
            <span style="font-size: 24px">{{ item.icon }}</span>
          </div>
        </div>
        <div class="text-subtitle2 text-weight-regular">
          {{ item.name }}
        </div>
      </CardItem>
    </div>
  </div>
</template>

<script setup lang="ts">
import CardItem from 'src/components/common/CardItem.vue';
import { useSportList } from 'src/pages/homepage/composables/useSportList';
import type { ISportProps } from 'src/pages/homepage/types/index';

const { activeSportId, setActiveSport } = useSportList();

const sportItems: ISportProps[] = [
  {
    id: '1',
    name: 'Cầu lông',
    icon: '🏸',
  },
  {
    id: '2',
    name: 'Pickleball',
    icon: '🏓',
  },
  {
    id: '3',
    name: 'Bóng đá  ',
    icon: '⚽',
  },
  {
    id: '4',
    name: 'Tennis',
    icon: '🎾',
  },
];
</script>

<style scoped lang="scss">
.card-sport {
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: none !important;
  background: transparent !important;
  .sport-icon-wrapper {
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: rgb(255, 237, 213);
    transition: all 0.2s ease;
    &.sport-active {
      background: rgb(249, 115, 22);
    }
  }
}
@media (min-width: 576px) {
  .card-sport {
    .sport-icon-wrapper {
      width: 64px;
      height: 64px;
    }
  }
}
</style>
