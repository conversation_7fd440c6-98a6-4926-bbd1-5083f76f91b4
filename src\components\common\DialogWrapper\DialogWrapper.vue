<template>
  <q-card 
    class="dialog-wrapper" 
    :class="$q.screen.lt.sm ? 'full-width' : ''"
    :style="dialogStyles"
  >
    <!-- Sticky Header -->
    <q-card-section 
      v-if="showHeader"
      class="sticky-top bg-white row items-center no-wrap shadow-2"
      :class="$q.screen.lt.sm ? 'q-py-sm' : 'q-pa-md'"
      style="flex-shrink: 0;"
    >
      <div class="text-h6 text-weight-bold text-grey-8"
        :class="$q.screen.lt.sm ? 'text-body1' : 'text-h6'"
      >
        {{ title }}
      </div>
      <q-space />
      <q-btn
        v-if="showCloseButton"
        icon="close"
        flat
        round
        size="md"
        @click="emit('close')"
        class="close-btn"
      />
    </q-card-section>

    <q-separator v-if="showHeader" />

    <!-- Scrollable Content Area -->
    <div 
      class="scroll-content" 
      :style="contentStyles"
    >
      <slot name="content" />
    </div>

    <q-separator v-if="showFooter" class="q-mt-md"/>

    <!-- Sticky Footer -->
    <q-card-actions 
      v-if="showFooter"
      class="sticky-bottom bg-white shadow-up-2 q-pa-md"
      style="flex-shrink: 0;"
    >
      <slot name="footer" />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from 'vue';
import { useQuasar } from 'quasar';
import type { DialogWrapperProps, DialogWrapperSlots } from './types';

// Component name for debugging
defineOptions({
  name: 'DialogWrapper'
});

// Props with defaults
const props = withDefaults(defineProps<DialogWrapperProps>(), {
  title: '',
  showHeader: true,
  showFooter: true,
  showCloseButton: true,
  maxWidth: '500px',
  minWidth: '300px',
  maxHeight: '90vh',
  fullHeight: true
});

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
}>();

// Slots
defineSlots<DialogWrapperSlots>();

// Composables
const $q = useQuasar();

// Computed properties
const dialogStyles = computed((): CSSProperties => ({
  maxHeight: props.maxHeight,
  maxWidth: $q.screen.lt.sm ? '100%' : props.maxWidth,
  minWidth: $q.screen.lt.sm ? '100%' : props.minWidth,
  display: 'flex',
  flexDirection: 'column',
  ...(props.fullHeight && { height: $q.screen.lt.sm ? '95vh' : 'auto' })
}));

const contentStyles = computed((): CSSProperties => ({
  flex: '1',
  overflowY: 'auto',
  overflowX: 'hidden',
  WebkitOverflowScrolling: 'touch',
  minHeight: '0'
}));
</script>

<style lang="scss" scoped>
.dialog-wrapper {
  border-radius: 16px;

  // Close button styling
  .close-btn {
    width: 40px;
    height: 40px;
    
    &:hover {
      background: var(--q-grey-2);
    }
  }

  // Scrollable content area
  .scroll-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    min-height: 0;
  }
}

// Mobile optimizations using Quasar breakpoints
@media (max-width: $breakpoint-xs-max) {
  .dialog-wrapper {
    margin: 0;
    border-radius: 16px 16px 0 0;
    max-height: 95vh;
  }
}

// Tablet and desktop enhancements
@media (min-width: $breakpoint-sm-min) {
  .dialog-wrapper {
    max-height: 80vh;
  }
}
</style>
