import { initializeApp } from 'firebase/app';
import { GoogleAuthProvider, getAuth } from 'firebase/auth';

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: import.meta.env.VITE_PUBLIC_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_PUBLIC_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Validate Firebase configuration
const validateFirebaseConfig = () => {
  const requiredFields = [
    'apiKey',
    'authDomain',
    'projectId',
    'storageBucket',
    'messagingSenderId',
    'appId'
  ];

  const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig]);

  if (missingFields.length > 0) {
    console.error('❌ Firebase configuration missing fields:', missingFields);
    console.error('🔍 Current config:', firebaseConfig);
    console.error('🌍 Environment variables:', {
      VITE_PUBLIC_FIREBASE_API_KEY: import.meta.env.VITE_PUBLIC_FIREBASE_API_KEY ? '✅ Set' : '❌ Missing',
      VITE_PUBLIC_FIREBASE_AUTH_DOMAIN: import.meta.env.VITE_PUBLIC_FIREBASE_AUTH_DOMAIN ? '✅ Set' : '❌ Missing',
      VITE_PUBLIC_FIREBASE_PROJECT_ID: import.meta.env.VITE_PUBLIC_FIREBASE_PROJECT_ID ? '✅ Set' : '❌ Missing',
      VITE_PUBLIC_FIREBASE_STORAGE_BUCKET: import.meta.env.VITE_PUBLIC_FIREBASE_STORAGE_BUCKET ? '✅ Set' : '❌ Missing',
      VITE_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: import.meta.env.VITE_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ? '✅ Set' : '❌ Missing',
      VITE_PUBLIC_FIREBASE_APP_ID: import.meta.env.VITE_PUBLIC_FIREBASE_APP_ID ? '✅ Set' : '❌ Missing',
    });
    throw new Error(`Firebase configuration incomplete. Missing: ${missingFields.join(', ')}`);
  }

  console.log('✅ Firebase configuration validated successfully');
};

// Validate configuration before initializing
validateFirebaseConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();

// Configure Google Auth Provider
googleProvider.setCustomParameters({
  prompt: 'select_account',
});

export default app;
