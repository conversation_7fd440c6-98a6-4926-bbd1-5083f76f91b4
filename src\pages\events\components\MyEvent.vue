<template>
  <div class="q-mb-lg">
    <div class="row q-gutter-sm q-my-md no-wrap overflow-scroll">
      <!-- Category -->
      <q-chip
        v-for="category in myEventsCategories"
        :key="category.id"
        :ripple="false"
        class="row items-center justify-between"
        :class="selectedMyEventCategory === category.id ? 'bg-primary-light' : 'bg-grey-3'"
        :text-color="selectedMyEventCategory === category.id ? 'primary' : 'black'"
        clickable
        @click="selectCategory(category.id)"
      >
        <span class="q-mr-sm"># {{ category.name }}</span>
      </q-chip>
    </div>
  </div>

  <!-- Events List -->
  <div class="row q-col-gutter-md">
    <div v-for="event in filteredEvents" :key="event.id" class="col-12 col-md-6">
      <EventCard :item="event" @click="navigateToEvent(event.id)" class="cursor-pointer">
        <template #participants v-if="event.status === 'new'">
          <ParticipantList
            :participants="event.participants"
            :totalParticipants="event.participants.length"
            :maxParticipants="3"
            :showSideActions="true"
          >
            <template #sideActions>
              <q-btn
                flat
                class="text-primary"
                :ripple="false"
                @click.stop="showParticipantsDialog(event as Event)"
              >
                Tất cả
              </q-btn>
            </template>
          </ParticipantList>
        </template>
        <template #footerActions>
          <div class="text-h6 text-primary">{{ formatPrice(event.price) }}</div>

          <div class="row items-center q-gutter-sm">
            <q-btn
              flat
              class="bg-grey-3"
              :color="event.status === 'completed' ? 'primary' : 'black'"
              :style="event.status === 'completed' ? 'background-color: #ffedd5 !important;' : ''"
              :label="getButtonLabel(event.status)"
              @click.stop="handleButtonClick(event as Event)"
            />
            <q-btn
              v-if="event.status === 'new'"
              flat
              :color="event.isPublic ? 'green' : 'red'"
              size="sm"
              :class="event.isPublic ? 'bg-green-1' : 'bg-red-1'"
              style="border-radius: 100%; width: 36px; height: 36px"
              :icon="event.isPublic ? 'public' : 'lock'"
              @click.stop="publicEvent(event.id)"
            />
          </div>
        </template>
      </EventCard>
    </div>
  </div>

  <!-- Empty State -->
  <div v-if="filteredEvents.length === 0" class="text-center q-py-xl">
    <EmptyData />
  </div>
  <!-- Rating Dialog -->
  <q-dialog v-model="ratingDialog" position="bottom">
    <RatingDialog :event="currentEvent as Event" @submit="handleRatingSubmit" />
  </q-dialog>

  <!-- Participants Dialog -->
  <q-dialog v-model="participantsDialog" position="bottom">
    <ParticipantsDialog
      :participants="currentEventParticipants"
      @hide="participantsDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import EventCard from 'src/components/EventCard.vue';
import ParticipantList from 'src/components/common/ParticipantList.vue';
import ParticipantsDialog from 'src/pages/events/components/dialogs/ParticipantsDialog.vue';
import RatingDialog from 'src/pages/events/components/dialogs/RatingDialog.vue';
import { type Event, useMyEvents } from 'src/pages/events/composables/useMyEvents';
import { ref } from 'vue';

const props = defineProps<{
  searchQuery: string;
  formatDate: (date: Date) => string;
  formatPrice: (price: number) => string;
}>();

const {
  selectedMyEventCategory,
  filteredEvents,
  ratingDialog,
  participantsDialog,
  currentEvent,
  currentEventParticipants,
  showParticipantsDialog,
  getButtonLabel,
  selectCategory,
  publicEvent,
  handleRatingSubmit,
  navigateToEvent,
  handleButtonClick,
  myEventsCategories,
} = useMyEvents(ref(props.searchQuery));
</script>

<style scoped>
.event-card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.bg-primary-light {
  background-color: #ffedd5 !important;
  border: 1px solid #fed7aa !important;
  box-shadow: none !important;
}

.overlapping {
  border: 2px solid white;
  position: absolute;
}

.event-image {
  position: relative;
}

.avatar-group {
  display: flex;
  align-items: center;

  .avatar-item {
    border: 2px solid white;

    img {
      object-fit: cover;
    }
  }

  .avatar-count {
    min-width: 24px;
    height: 24px;
    margin: 0 !important;
    margin-top: 4px !important;
    margin-left: -12px !important;
    z-index: 1000;
    border-radius: 100%;
  }
}
</style>
