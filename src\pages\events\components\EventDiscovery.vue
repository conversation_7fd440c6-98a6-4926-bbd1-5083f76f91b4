<template>
  <div class="q-mb-lg">
    <!-- Category chips with horizontal scroll on mobile -->
    <div class="category-section q-my-md">
      <div class="category-scroll-container">
        <div class="category-chips-wrapper">
          <q-chip
            v-for="category in eventDiscoveryQuickTags"
            :key="category.id"
            :outline="selectedQuickTags.includes(category.id)"
            :color="selectedQuickTags.includes(category.id) ? 'primary' : 'grey-3'"
            :text-color="selectedQuickTags.includes(category.id) ? 'white' : 'black'"
            clickable
            @click="toggleCategory(category.id)"
            :class="{ 'category-chip--active': selectedQuickTags.includes(category.id) }"
          >
            <q-icon
              v-if="selectedQuickTags.includes(category.id)"
              name="check_circle"
              size="16px"
              class="q-mr-xs"
            />
            {{ category.name }}
          </q-chip>
        </div>
      </div>
    </div>
  </div>

  <!-- Events List - Card View -->
  <div v-if="props.viewMode === 'card'" class="row q-col-gutter-md">
    <div v-for="event in filteredEvents" :key="event.id" class="col-12 col-sm-6 col-md-6">
      <EventCard
        :item="event"
        @click.stop="viewEvent(event.id)"
        @report="handleReportEvent"
        class="event-card cursor-pointer"
      >
        <template #participants>
          <ParticipantList
            :participants="event.participants"
            :totalParticipants="event.participants.length"
            :maxParticipants="3"
            :showSideActions="true"
          >
            <template #sideActions>
              <q-btn
                dense
                flat
                class="text-primary"
                size="sm"
                :ripple="false"
                @click.stop="showParticipantsDialog(event)"
              >
                <q-icon name="visibility" size="12px" color="primary" class="q-mr-xs" />
                Xem
              </q-btn>
            </template>
          </ParticipantList>
        </template>
        <template #footerActions>
          <div class="">
          <q-btn flat dense no-caps>
            HÔM NAY
           </q-btn>
            <q-btn flat dense no-caps>
              5:30PM
            </q-btn>
            <q-btn flat dense no-caps>
              7:00PM
            </q-btn>
            </div>
            <q-btn 
              flat
              no-caps
              style="background: rgba(255, 129, 66, 0.12) !important;" 
              color="primary" 
              class="book-btn"
              dense
              @click.stop="handleJoinEvent(event)"
            >
              Đặt chỗ
            </q-btn>
          </template>
      </EventCard>
    </div>
  </div>
  <!-- Events List - List View -->
  <div v-if="props.viewMode === 'list'" class="events-list-view">
    <div v-for="event in filteredEvents" :key="event.id" class="event-list-item">
      <EventCardHorizontal
        :item="event"
        @click.stop="viewEvent(event.id)"
        @report="handleReportEvent"
        class="cursor-pointer"
      >
        <!-- Enhanced Participants Display for List View -->
        <template #participants>
          <div class="participants-section-list">
            <!-- Participants Header -->
            <div class="participants-header row items-center justify-between">
              <div class="row items-center">
                <q-icon name="group" size="14px" color="primary" class="q-mr-xs" />
                <span class="participants-count text-caption text-weight-medium">
                  {{ event.participants.length }}/{{ event.maxParticipants }} joined
                </span>
              </div>
              <q-btn
                v-if="event.participants.length > 0"
                flat
                dense
                no-caps
                size="xs"
                color="primary"
                class="view-all-btn"
                @click.stop="showParticipantsDialog(event)"
              >
                <span class="text-caption">Xem</span>
              </q-btn>
            </div>

            <!-- Participants Avatars (Compact) -->
            <div v-if="event.participants.length > 0" class="participants-avatars row items-center">
              <div class="avatar-group">
                <q-avatar
                  v-for="(participant, index) in event.participants.slice(0, 3)"
                  :key="participant.id"
                  size="20px"
                  class="participant-avatar"
                  :style="{
                    marginLeft: index === 0 ? '0' : '-6px',
                    zIndex: event.participants.length - index
                  }"
                >
                  <img :src="participant.avatar || '/images/default-avatar.png'" :alt="participant.name" />
                  <q-tooltip class="bg-grey-9 text-white" anchor="top middle" self="bottom middle">
                    {{ participant.name }}
                  </q-tooltip>
                </q-avatar>

                <!-- More participants indicator -->
                <div
                  v-if="event.participants.length > 3"
                  class="more-participants"
                  :style="{ marginLeft: '-6px', zIndex: 1 }"
                >
                  <q-avatar size="20px" color="grey-4" text-color="grey-7">
                    <span class="text-caption text-weight-bold">+{{ event.participants.length - 3 }}</span>
                  </q-avatar>
                </div>
              </div>

              <!-- Participants Names (Mobile) -->
              <div class="participants-names q-ml-sm">
                <span class="text-caption text-grey-6">
                  {{ getParticipantNamesText(event.participants) }}
                </span>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="empty-participants">
              <span class="text-caption text-grey-5">No participants yet</span>
            </div>
          </div>
        </template>

        <!-- Streamlined Actions for List View -->
        <template #actions>
          <div class="list-actions">
            <!-- Quick Time Slots -->
            <div class="time-slots">
              <q-chip
                v-for="slot in event.timeSlots?.slice(0, 2)"
                :key="slot"
                size="sm"
                outline
                color="grey-6"
                text-color="grey-8"
                class="time-chip"
              >
                {{ slot }}
              </q-chip>
              <q-chip
                v-if="event.timeSlots?.length && event.timeSlots.length > 2"
                size="sm"
                outline
                color="grey-6"
                text-color="grey-8"
                class="time-chip"
              >
                +{{ event.timeSlots.length - 2 }}
              </q-chip>
            </div>

            <!-- Primary Action -->
            <q-btn
              unelevated
              size="sm"
              no-caps
              class="book-btn"
              @click.stop="handleJoinEvent(event)"
            >
              Đặt chỗ
            </q-btn>
          </div>
        </template>
      </EventCardHorizontal>
    </div>
  </div>
  <!-- Empty State -->
  <div v-if="filteredEvents.length === 0" class="text-center q-py-xl">
    <EmptyData />
  </div>

  <!-- Dialog filter -->
  <q-dialog v-model="dialog" :position="position">
    <FilterDialog @apply="handleFilterApply" @close="dialog = false" />
  </q-dialog>

  <!-- Participants Dialog -->
  <q-dialog v-model="participantsDialog" position="bottom">
    <ParticipantsDialog
      :participants="currentEvent?.participants"
      @hide="participantsDialog = false"
    />
  </q-dialog>

  <!-- Report Dialog -->
  <q-dialog
    v-model="reportDialog"
    :full-width="$q.screen.lt.sm"
  >
    <ReportDialog
      :target-id="reportTargetId"
      target-type="event"
      @close="reportDialog = false"
      @submit="handleReportSubmit"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import EventCard from 'src/components/EventCard.vue';
import EventCardHorizontal from 'src/components/EventCardHorizontal.vue';
import ParticipantList from 'src/components/common/ParticipantList.vue';
import type { EventJoinData, useEventJoinFlow } from 'src/composables/events/useEventJoinFlow';
import FilterDialog from 'src/components/FilterDialog.vue';
import ParticipantsDialog from 'src/pages/events/components/dialogs/ParticipantsDialog.vue';
import ReportDialog from 'src/components/common/ReportDialog.vue';
import EmptyData from 'src/components/EmptyData.vue';
import { useDiscoveryEvent } from 'src/pages/events/composables/useDiscoveryEvent';
import { inject, ref, watch } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

const dialog = ref(false);
const position = 'bottom';
const dateFilter = ref('today');

// Inject the event join flow
const eventJoinFlow = inject<ReturnType<typeof useEventJoinFlow>>('eventJoinFlow');

const props = defineProps<{
  searchQuery: string;
  formatDate: (date: Date) => string;
  formatPrice: (price: number) => string;
  closeDialogs: boolean;
  viewMode: string;
}>();

const emit = defineEmits(['dialogStateChanged']);

// Applied filters state
interface AppliedFilters {
  sport: string | null;
  skillLevels: string[];
  dateType: string | null;
  date: string | null;
  distance: { min: number; max: number } | null;
  location: string | null;
}

// Report data interface
interface ReportData {
  option: string;
  details: string;
  targetId?: string | number | undefined;
  targetType: string;
}

const appliedFilters = ref<AppliedFilters>({
  sport: null,
  skillLevels: [],
  dateType: null,
  date: null,
  distance: null,
  location: null,
});

const {
  filteredEvents,
  selectedQuickTags,
  participantsDialog,
  currentEvent,
  eventDiscoveryQuickTags,
  toggleCategory,
  showParticipantsDialog,
  viewEvent,
} = useDiscoveryEvent(ref(props.searchQuery));

// Report dialog state
const reportDialog = ref(false);
const reportTargetId = ref<string | number>('');

watch(dialog, (newValue) => {
  emit('dialogStateChanged', newValue);
});

watch(
  () => props.closeDialogs,
  (shouldClose) => {
    if (shouldClose && dialog.value) {
      dialog.value = false;
    }
  },
);

// Filter management methods

const handleFilterApply = (filters) => {
  console.log('Applied filters:', filters);

  // Update the date filter based on the enhanced selection
  if (filters.dateType) {
    dateFilter.value = filters.dateType;
  }

  // Store applied filters for state tracking
  appliedFilters.value = {
    sport: filters.sport !== 'badminton' ? filters.sport : null,
    skillLevels:
      filters.skillLevels &&
      filters.skillLevels.length > 0 &&
      !(filters.skillLevels.length === 1 && filters.skillLevels[0] === 'all')
        ? filters.skillLevels
        : [],
    dateType: filters.dateType !== 'today' ? filters.dateType : null,
    date: filters.date,
    distance:
      filters.distance && (filters.distance.min !== 0 || filters.distance.max !== 15)
        ? filters.distance
        : null,
    location: filters.location !== 'New York, USA' ? filters.location : null,
  };

  // Close dialog
  dialog.value = false;
};

// Helper function for participant names display
const getParticipantNamesText = (participants: any[]) => {
  if (participants.length === 0) return '';

  if (participants.length === 1) {
    return participants[0].name;
  }

  if (participants.length === 2) {
    return `${participants[0].name}, ${participants[1].name}`;
  }

  return `${participants[0].name} and ${participants.length - 1} others`;
};

// Handle join event button click
const handleJoinEvent = (event) => {
  if (!eventJoinFlow) {
    // Fallback if join flow is not available
    $q.dialog({
      title: 'Xác nhận đăng ký',
      message: `Bạn có muốn đăng ký tham gia sự kiện "${event.title}"?`,
      cancel: true,
      persistent: true,
    }).onOk(() => {
      $q.notify({
        message: 'Đăng ký thành công!',
        color: 'positive',
        position: 'top',
        timeout: 2000,
      });
    });
    return;
  }

  const eventData: EventJoinData = {
    id: event.id,
    title: event.title,
    price: event.price,
    date: event.date,
    startTime: event.startTime,
    endTime: event.endTime,
    photoUrl: event.photoUrl,
    maxSlots: event.totalParticipants,
    organizer: {
      name: 'Host',
      phoneNumber: event.hostPhoneNumber || '+1 (212) 2222222',
    },
  };

  eventJoinFlow.joinEvent(eventData);
};

// Handle report event
const handleReportEvent = (eventId: string | number) => {
  reportTargetId.value = eventId;
  reportDialog.value = true;
};

// Handle report submission
const handleReportSubmit = (reportData: ReportData) => {
  console.log('Report submitted:', reportData);

  // Here you would typically send the report to your backend
  // For now, we'll just show a success message
  $q.notify({
    type: 'positive',
    message: 'Báo cáo đã được gửi thành công',
    position: 'top',
    timeout: 3000
  });

  reportDialog.value = false;
};
</script>

<style lang="scss" scoped>

// Badge pulse animation
@keyframes pulse-badge {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// Category section with horizontal scroll
.category-section {
  .category-scroll-container {
    overflow-x: auto;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge

    &::-webkit-scrollbar {
      display: none; // Chrome/Safari
    }
  }

  .category-chips-wrapper {
    display: flex;
    gap: 8px;
    padding: 2px 0;
    min-width: max-content;

    .category-chip {
      flex-shrink: 0;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:not(.category-chip--active) {
        background-color: #f8f9fa;
        color: #6c757d;
        border-color: #e9ecef;

        &:hover {
          background-color: #e9ecef;
          color: #495057;
        }
      }

      &.category-chip--active {
        background-color: var(--q-primary);
        color: white;
        box-shadow: 0 2px 8px rgba(255, 129, 66, 0.25);
        transform: translateY(-1px);
      }
    }
  }
}

.event-card {
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.01);
  }
}

.avatar-group {
  display: flex;
  align-items: center;

  .avatar-item {
    border: 2px solid white;

    img {
      object-fit: cover;
    }
  }

  .avatar-count {
    min-width: 24px;
    height: 24px;
    margin: 0 !important;
    margin-top: 4px !important;
    margin-left: -12px !important;
    z-index: 1000;
    border-radius: 100%;
  }
}

// Responsive breakpoints
@media (max-width: 599px) {

  .category-section {
    margin-left: -16px;
    margin-right: -16px;

    .category-scroll-container {
      padding: 0 16px;
    }
  }
}

// Tablet and desktop optimizations
@media (min-width: 768px) {

  .category-section {
    .category-chips-wrapper {
      gap: 12px;

      .category-chip {
        font-size: 15px;
        padding: 10px 20px;
      }
    }
  }
}

// List View Styling - Professional & Optimized
.events-list-view {
  .event-list-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
  // Enhanced Participants Section for List View
  .participants-section-list {
    .participants-header {
      .participants-count {
        font-size: 11px;
        color: #666;
        font-weight: 600;
      }

      .view-all-btn {
        min-height: 18px;
        padding: 2px 6px;
        border-radius: 10px;

        &:hover {
          background: rgba(255, 129, 66, 0.1);
        }
      }
    }

    .participants-avatars {
      .avatar-group {
        display: flex;
        align-items: center;

        .participant-avatar {
          border: 2px solid white;
          transition: transform 0.2s ease;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: scale(1.1);
            z-index: 100 !important;
          }

          img {
            object-fit: cover;
          }
        }

        .more-participants {
          .q-avatar {
            border: 2px solid white;
            font-size: 9px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .participants-names {
        flex: 1;
        min-width: 0;

        span {
          font-size: 10px;
          line-height: 1.3;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }

    .empty-participants {
      padding: 6px 0;
      text-align: center;

      span {
        font-size: 10px;
        font-style: italic;
      }
    }
  }
  
}
</style>
