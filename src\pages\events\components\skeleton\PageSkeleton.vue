<template>
  <div class="page-skeleton">
    <!-- Search Section Skeleton -->
    <section class="bg-white q-px-md q-py-md">
      <div class="row justify-center">
        <div class="col-12">
          <q-skeleton
            type="QInput"
            class="search-skeleton"
            height="48px"
            style="border-radius: 24px"
          />
        </div>
      </div>
    </section>

    <!-- Tabs Section Skeleton -->
    <section class="bg-white q-px-md tabs-skeleton-section">
      <div class="row items-center justify-between">
        <div class="tabs-skeleton">
          <q-skeleton
            type="text"
            width="100px"
            height="20px"
            class="tab-skeleton"
          />
          <q-skeleton
            type="text"
            width="140px"
            height="20px"
            class="tab-skeleton"
          />
        </div>
      </div>
    </section>

    <!-- Tab Content Skeleton -->
    <section class="tab-content bg-white">
      <div class="events-content q-px-md q-mt-sm">
        <!-- Header Title Skeleton -->
        <div class="header-skeleton q-mb-md q-mt-sm">
          <q-skeleton type="text" width="180px" height="24px" class="q-mb-sm" />
        </div>

        <!-- Category Chips Skeleton -->
        <div class="category-skeleton q-mb-md">
          <div class="row q-gutter-sm no-wrap">
            <q-skeleton
              type="QChip"
              width="80px"
              height="32px"
              v-for="n in 4"
              :key="n"
              style="border-radius: 16px"
            />
          </div>
        </div>

        <!-- Events Cards Skeleton -->
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6" v-for="n in 4" :key="n">
            <q-card class="event-card-skeleton">
              <!-- Event Image Skeleton -->
              <q-skeleton height="160px" square />

              <!-- Event Content Skeleton -->
              <q-card-section>
                <q-skeleton type="text" width="70%" height="20px" class="q-mb-sm" />
                <q-skeleton type="text" width="50%" height="16px" class="q-mb-md" />

                <!-- Participants Skeleton -->
                <div class="row items-center justify-between q-mb-md">
                  <div class="row items-center">
                    <q-skeleton
                      type="circle"
                      size="24px"
                      v-for="p in 3"
                      :key="p"
                      class="participant-avatar"
                      :style="{ marginLeft: p > 1 ? '-8px' : '0' }"
                    />
                    <q-skeleton type="text" width="60px" height="14px" class="q-ml-sm" />
                  </div>
                </div>

                <!-- Footer Actions Skeleton -->
                <div class="row items-center justify-between">
                  <q-skeleton type="text" width="80px" height="18px" />
                  <q-skeleton type="QBtn" width="90px" height="32px" style="border-radius: 16px" />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- FAB Skeleton -->
    <div class="fixed-bottom-right">
      <q-skeleton
        type="circle"
        size="56px"
        class="fab-skeleton"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
// No props or logic needed
</script>

<style lang="scss" scoped>
.page-skeleton {
  background: #f5f5f5;
  min-height: 100vh;
}

// Search Section Styling
.search-skeleton {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

// Tabs Section Styling
.tabs-skeleton-section {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 16px;
}

.tabs-skeleton {
  display: flex;
  gap: 24px;
  align-items: center;

  .tab-skeleton {
    border-bottom: 3px solid transparent;
    padding-bottom: 8px;

    &:first-child {
      border-bottom-color: #ff8142;
    }
  }
}

// Content Styling
.tab-content {
  min-height: calc(100vh - 200px);
}

.events-content {
  background: white;
}

.header-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-skeleton {
  overflow-x: auto;

  .row {
    min-width: max-content;
  }
}

.event-card-skeleton {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  .participant-avatar {
    border: 2px solid white;
    z-index: 1;
  }
}

// FAB Styling
.fixed-bottom-right {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.fab-skeleton {
  box-shadow: 0 4px 16px rgba(255, 129, 66, 0.3);
}

// General Skeleton Styling
.q-skeleton {
  border-radius: 8px;

  &--type-QChip {
    height: 32px;
  }

  &--type-QBtn {
    height: 36px;
  }

  &--type-circle {
    border-radius: 50%;
  }
}

// Mobile Responsive
@media (max-width: 599px) {
  .tabs-skeleton {
    gap: 16px;

    .tab-skeleton {
      padding-bottom: 6px;
    }
  }

  .event-card-skeleton {
    margin-bottom: 16px;
  }

  .fixed-bottom-right {
    bottom: 20px;
    right: 20px;
  }
}
</style>
