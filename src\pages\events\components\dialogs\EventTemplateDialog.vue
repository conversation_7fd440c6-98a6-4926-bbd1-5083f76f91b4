<template>
  <q-card style="width: 100%; max-width: 600px">
    <q-card-section class="row items-center q-pb-none no-border-radius">
      <div class="text-h6">Chọn mẫu hoạt động</div>
      <q-space />
      <q-btn icon="close" flat round dense v-close-popup @click="emit('hide')" />
    </q-card-section>

    <q-card-section>
      <q-input
        v-model="searchQuery"
        outlined
        dense
        placeholder="Tìm kiếm mẫu hoạt động..."
        class="q-mb-md search-input"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

      <q-scroll-area style="height: 400px">
        <q-list>
          <q-item
            v-for="template in filteredTemplates"
            :key="template.id"
            clickable
            :to="`/events/create?template=${template.id}`"
            class="q-py-md q-mb-md template-item text-black text-body1 bg-grey-1"
            style="border-radius: 8px"
          >
            <q-item-section avatar>
              <q-avatar size="48px" square style="border-radius: 8px">
                <img :src="template.image" alt="activity icon" />
              </q-avatar>
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-weight-bold">{{ template.title }}</q-item-label>
              <q-item-label caption>{{ template.location }}</q-item-label>
            </q-item-section>

            <q-item-section side class="text-black-4">
              <q-item-label caption>{{ template.date }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-card-section>
    <q-separator />
    <q-card-section class="text-center">
      <div class="text-black-4 q-pa-none">Chọn mẫu sự kiện để tạo sự kiện mới</div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface Emits {
  (e: 'ok', template: any): void;
  (e: 'hide'): void;
}

const emit = defineEmits<Emits>();

const searchQuery = ref('');
const templates = ref([
  {
    id: 1,
    title: 'Morning Basketball Training',
    location: 'Central Sports Complex',
    date: 'Today',
    image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400',
  },
  {
    id: 2,
    title: 'Weekend Tennis Match',
    location: 'Tennis Center',
    date: 'Saturday',
    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400',
  },
  {
    id: 3,
    title: 'Badminton Club Championship',
    location: 'Sports Arena',
    date: 'Sunday',
    image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400',
  },
  {
    id: 4,
    title: 'Evening Basketball Game',
    location: 'Local Gym',
    date: 'Yesterday',
    image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400',
  },
  {
    id: 5,
    title: 'Tennis Singles Tournament',
    location: 'Tennis Club',
    date: 'Tomorrow',
    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400',
  },
  {
    id: 6,
    title: 'Badminton Championship Qualifier',
    location: 'Elite Sports Center',
    date: 'Next Week',
    image: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400',
  },
  {
    id: 7,
    title: 'Evening Basketball League',
    location: 'Downtown Sports Center',
    date: 'Last Friday',
    image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400',
  },
]);

const filteredTemplates = computed(() => {
  if (!searchQuery.value) return templates.value;

  const query = searchQuery.value.toLowerCase();
  return templates.value.filter(
    (template) =>
      template.title.toLowerCase().includes(query) ||
      template.location.toLowerCase().includes(query),
  );
});
</script>

<style lang="scss" scoped>
.template-item {
  &:hover {
    background-color: #fff7ed;
  }
  ::v-deep(.q-focus-helper) {
    opacity: 0 !important;
  }
}
.search-input {
  ::v-deep(.q-field__control) {
    background-color: #f3f4f6 !important;
    border-radius: 8px;
    &:before {
      border: none !important;
    }
  }
}
</style>
