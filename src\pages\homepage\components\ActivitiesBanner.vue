<template>
  <!-- Sport Slide -->
  <BaseSlider :itemsSlide="activities">
    <template #default="{ item }">
      <CardItem
        :item="item"
        :cardProps="{ containerClass: 'card-activity', hasCustomShadow: true }"
      >
        <router-link :to="`/activities/${item.id}`" class="column justify-between">
          <div class="row items-center justify-between" style="padding: 8px 12px">
            <div class="row items-center" style="gap: 12px">
              <!-- <img
                :src="item.imageUrl"
                alt="activity"
                class="activity-image"
                width="32px"
                height="32px"
              /> -->
              <Volleyball :size="32" color="#1d1d1d" />
              <div class="column justify-between">
                <div class="text-body2 text-bold" style="color: #1d1d1d !important">
                  {{ item.title }}
                </div>
                <div class="text-caption text-grey-8">{{ item.description }}</div>
              </div>
            </div>
            <ChevronRight color="#1d1d1d" />
          </div>
        </router-link>
      </CardItem>
    </template>
  </BaseSlider>
</template>

<script setup lang="ts">
import { ChevronRight, Volleyball } from 'lucide-vue-next';
import BaseSlider from 'src/components/common/BaseSlider.vue';
import CardItem from 'src/components/common/CardItem.vue';
import type { IActivitiesBannerProps } from 'src/pages/homepage/types/index';

const activities: IActivitiesBannerProps[] = [
  {
    id: '1',
    title: 'G rand Sports Center1',
    description: 'Bắt đầu sau 1 giờ - 10:00',
    imageUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
  },
  {
    id: '2',
    title: 'G rand Sports Center2',
    description: 'Bắt đầu sau 1 giờ - 10:00',
    imageUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
  },
  {
    id: '3',
    title: 'G rand Sports Center3',
    description: 'Bắt đầu sau 1 giờ - 10:00',
    imageUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
  },
  {
    id: '4',
    title: 'G rand Sports Center4',
    description: 'Bắt đầu sau 1 giờ - 10:00',
    imageUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
  },
];
</script>
<style scoped lang="scss">
.card-activity {
  border-radius: 9999px !important;
  border: 1px solid rgb(253, 186, 116);
}
</style>
