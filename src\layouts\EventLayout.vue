<template>
  <q-layout class="q-border bg-grey-2" view="lHh Lpr lFf">
    <q-page-container class="q-page-container">
      <router-view />
    </q-page-container>

    <!-- Global Event Join Dialog Components -->
    <EventJoinDialog
      v-if="joinFlow.selectedEvent"
      v-model="showJoinDialog"
      :event="unref(joinFlow.selectedEvent)"
      @confirm="joinFlow.confirmJoin"
      @cancel="joinFlow.resetDialogs"
    />

    <ConfirmDialog
      v-model="showConfirmDialog"
      :phone-number="unref(joinFlow.formData)?.organizerPhoneNumber || ''"
      @confirm="joinFlow.finalConfirm"
      @cancel="joinFlow.cancelConfirm"
    />

    <SuccessDialog v-model="showSuccessDialog" @confirm="joinFlow.completeBooking" />
  </q-layout>
</template>

<script setup lang="ts">
import { useEventJoinFlow } from 'src/composables/events/useEventJoinFlow';
import ConfirmDialog from 'src/pages/events/components/dialogs/ConfirmDialog.vue';
import EventJoinDialog from 'src/components/EventJoinDialog.vue';
import SuccessDialog from 'src/pages/events/components/dialogs/SuccessDialog.vue';
import { computed, provide, unref } from 'vue';

// Create the join flow instance
const joinFlow = useEventJoinFlow();

// Dialog state using computed with getter/setter
const showJoinDialog = computed({
  get: () => joinFlow.showJoinDialog.value,
  set: (value) => {
    joinFlow.showJoinDialog.value = value;
  },
});

const showConfirmDialog = computed({
  get: () => joinFlow.showConfirmDialog.value,
  set: (value) => {
    joinFlow.showConfirmDialog.value = value;
  },
});

const showSuccessDialog = computed({
  get: () => joinFlow.showSuccessDialog.value,
  set: (value) => {
    joinFlow.showSuccessDialog.value = value;
  },
});

// Provide the join flow to all child components
provide('eventJoinFlow', joinFlow);
</script>

<style lang="scss" scoped>
.q-page-container {
  max-width: 690px;
  margin: 0 auto;
}
</style>
