<template>
  <div class="section-header">
    <div class="title-wrapper">
      <component 
        v-if="icon" 
        :is="icon" 
        :size="iconSize || 20" 
        :color="iconColor || '#ff8142'" 
        class="section-icon"
      />
      <h3 class="section-title">{{ title }}</h3>
      <q-badge v-if="badge" color="primary" text-color="white" class="badge-hot">
        {{ badge }}
      </q-badge>
    </div>
    
    <q-btn
      v-if="viewAll"
      flat
      no-caps
      :to="viewAllLink"
      class="view-all-btn"
    >
      <span>{{ viewAllLabel || 'Xem tất cả' }}</span>
      <ChevronRight v-if="showArrow" :size="16" color="#ff8142" class="q-ml-xs" />
    </q-btn>
  </div>
</template>

<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next';
import type { Component } from 'vue';

interface Props {
  title: string;
  icon?: Component | undefined;
  iconSize?: number | undefined;
  iconColor?: string | undefined;
  badge?: string | undefined;
  viewAll?: boolean | undefined;
  viewAllLink?: string | undefined;
  viewAllLabel?: string | undefined;
  showArrow?: boolean | undefined;
}

defineProps<Props>();
</script>

<style lang="scss" scoped>
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  .title-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .section-icon {
      flex-shrink: 0;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: 700;
      color: #212529;
      margin: 0;
    }
    
    .badge-hot {
      text-transform: uppercase;
      font-size: 10px;
      font-weight: 600;
      padding: 2px 4px;
      border-radius: 4px;
    }
  }
  
  .view-all-btn {
    font-size: 14px;
    font-weight: 500;
    color: #ff8142;
    padding: 4px 6px;
    display: flex;
    align-items: center;
  }
}
</style> 