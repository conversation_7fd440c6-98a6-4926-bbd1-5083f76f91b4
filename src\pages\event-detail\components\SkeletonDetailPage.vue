<template>
  <q-page class="card-detail-skeleton-mobile">
    <!-- Fixed Header Image Skeleton -->
    <div class="header-image-skeleton-fixed">
      <q-skeleton animation="wave" class="full-width full-height" />

      <!-- Back and Share buttons -->
      <div
        class="absolute-top q-mt-md q-px-md q-py-xs row items-center justify-between"
        style="z-index: 10"
      >
        <q-skeleton type="circle" size="40px" />
        <div class="row q-gutter-sm">
          <q-skeleton type="circle" size="40px" />
          <q-skeleton type="circle" size="40px" />
        </div>
      </div>

      <!-- Price chip -->
      <div class="absolute-bottom-left q-ma-sm">
        <q-skeleton type="QChip" width="120px" height="32px" />
      </div>
    </div>

    <!-- Scrollable Content Area -->
    <div class="content-skeleton-scrollable">
      <div class="card-inner-skeleton">
        <!-- Tags -->
        <div class="row items-center q-gutter-x-sm q-mb-md">
          <q-skeleton type="QChip" width="100px" v-for="n in 3" :key="n" />
        </div>

        <!-- Title -->
        <div class="row items-center justify-between full-width no-wrap q-mb-sm">
          <q-skeleton type="text" class="text-h5" width="80%" />
          <q-skeleton type="QBadge" width="40px" height="40px" />
        </div>

        <!-- Participants Section - Updated -->
        <div class="participants-section q-pa-md q-mb-lg" style="background: #f8f9fa; border-radius: 12px;">
          <!-- Header with Icon and Count -->
          <div class="row items-center justify-between q-mb-md">
            <div class="row items-center">
              <q-skeleton type="rect" size="20px" class="q-mr-sm" />
              <q-skeleton type="text" width="80px" />
              <q-skeleton type="QBadge" width="40px" height="20px" class="q-ml-sm" />
            </div>
            <q-skeleton type="text" width="60px" />
          </div>

          <!-- Participants List -->
          <div class="row items-center justify-between">
            <!-- Avatar Group -->
            <div class="row items-center">
              <q-skeleton type="QAvatar" size="40px" v-for="n in 4" :key="n"
                :style="{ marginLeft: n === 1 ? '0' : '-8px', zIndex: 10 - n }" />
              <q-skeleton type="QAvatar" size="40px" style="margin-left: -8px;" />
            </div>
            <!-- View All Button -->
            <q-skeleton type="QBtn" width="70px" height="32px" />
          </div>

          <!-- Participants Names (Mobile) -->
          <div class="q-mt-sm">
            <q-skeleton type="text" width="50%" />
          </div>
        </div>

        <!-- Optimized Description Section -->
        <div class="description-section q-mb-lg">
          <div class="description-content-compact">
            <q-skeleton type="text" width="100%" class="q-mb-xs" v-for="n in 3" :key="n" />
            <div class="row items-center q-gutter-xs">
              <q-skeleton type="text" width="60%" />
              <q-skeleton type="QBtn" width="60px" height="24px" />
            </div>
          </div>
        </div>

        <!-- Event Details Section -->
        <div class="event-details-section q-pa-md q-mb-lg" style="background: #f8f9fa; border-radius: 12px;">
          <div class="column q-gutter-md">
            <!-- Location -->
            <div class="row items-center justify-between">
              <q-skeleton type="text" width="50px" />
              <div class="row items-center q-gutter-xs">
                <q-skeleton type="circle" size="32px" />
                <q-skeleton type="text" width="120px" />
              </div>
            </div>

            <!-- Price -->
            <div class="row items-center justify-between">
              <q-skeleton type="text" width="70px" />
              <q-skeleton type="text" width="80px" />
            </div>

            <!-- Date -->
            <div class="row items-center justify-between">
              <q-skeleton type="text" width="60px" />
              <q-skeleton type="text" width="100px" />
            </div>

            <!-- Time -->
            <div class="row items-center justify-between">
              <q-skeleton type="text" width="50px" />
              <q-skeleton type="text" width="90px" />
            </div>
          </div>
        </div>

        <!-- Organizer Section - Updated -->
        <div class="organizer-section q-my-md">
          <div class="row items-center justify-between">
            <div class="row items-center q-gutter-x-sm">
              <q-skeleton type="QAvatar" size="48px" />
              <div>
                <q-skeleton type="text" width="120px" class="q-mb-xs" />
                <q-skeleton type="text" width="80px" />
              </div>
            </div>
            <div class="row q-gutter-sm">
              <q-skeleton type="circle" size="40px" />
              <q-skeleton type="circle" size="40px" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Responsive Footer Actions -->
    <q-page-sticky
      position="bottom"
      expand
      class="bottom-toolbar-skeleton bg-white justify-between q-pa-md"
    >
      <div class="row items-center justify-between full-width q-gutter-x-md">
        <!-- Price Section -->
        <div class="row items-baseline">
          <q-skeleton type="text" width="80px" height="32px" />
          <q-skeleton type="text" width="40px" height="16px" class="q-ml-xs" />
        </div>
        <!-- Action Button -->
        <q-skeleton type="QBtn" width="120px" height="40px" />
      </div>
    </q-page-sticky>
  </q-page>
</template>

<script setup lang="ts">
// No props or logic needed
</script>

<style lang="scss" scoped>
// Mobile-First Skeleton Layout
.card-detail-skeleton-mobile {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
  overflow: hidden;
}

// Fixed Header Image Skeleton
.header-image-skeleton-fixed {
  position: relative;
  height: 18rem;
  flex-shrink: 0;
  z-index: 2;
}

// Scrollable Content Skeleton
.content-skeleton-scrollable {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  background: white;

  &::-webkit-scrollbar {
    display: none;
  }
}

// Card Inner Skeleton
.card-inner-skeleton {
  position: relative;
  z-index: 1;
  background-color: white;
  border-radius: 24px 24px 0 0;
  padding: 20px;
  min-height: calc(100vh - 18rem - 80px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

// Fixed Footer Skeleton
.bottom-toolbar-skeleton {
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 690px !important;
  margin: 0 auto;
  z-index: 1000;
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

// Mobile Responsive
@media (max-width: 768px) {
  .header-image-skeleton-fixed {
    height: 16rem;
  }

  .card-inner-skeleton {
    border-radius: 20px 20px 0 0;
    padding: 16px;
    min-height: calc(100vh - 16rem - 80px);
  }
}

@media (max-width: 480px) {
  .header-image-skeleton-fixed {
    height: 14rem;
  }

  .card-inner-skeleton {
    border-radius: 16px 16px 0 0;
    padding: 12px;
    min-height: calc(100vh - 14rem - 70px);
  }
}

.q-skeleton {
  border-radius: 8px;

  &--type-QChip {
    height: 32px;
  }

  &--type-QBtn {
    height: 36px;
  }

  &--type-QAvatar {
    border-radius: 50%;
  }

  &--type-circle {
    border-radius: 50%;
  }
}
</style>
