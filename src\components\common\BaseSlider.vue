<!-- BaseSlider.vue -->
<template>
  <swiper
    :slidesPerView="isMobile && itemsSlide.length > 2 ? 'auto' : 2"
    :modules="modules"
    class="mySwiper q-pa-md"
    :spaceBetween="20"
  >
    <swiper-slide v-for="(item, index) in itemsSlide" :key="index">
      <slot :item="item" :index="index" />
    </swiper-slide>
  </swiper>
</template>

<script setup lang="ts">
import 'swiper/css';
import 'swiper/css/pagination';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { onMounted, onUnmounted, ref } from 'vue';

defineProps<{
  itemsSlide: any[];
}>();

const modules = [Pagination];
const isMobile = ref(window.innerWidth < 768);

const handleResize = () => {
  isMobile.value = window.innerWidth < 768;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.swiper {
  width: 100%;
  padding: 16px 1px;
}

.swiper-slide {
  width: 300px;
  height: 100%;
}
</style>
