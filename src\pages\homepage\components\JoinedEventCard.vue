<template>
  <q-card class="joined-event-card" @click="$emit('click')">
    <div class="card-image">
      <q-img
        :src="event.image"
        :alt="event.title"
        class="event-image"
        loading="lazy"
      >
        <div class="status-badge" :class="`status-${event.status}`">
          {{ statusText }}
        </div>
      </q-img>
    </div>
    
    <q-card-section class="card-content">
      <div class="event-title">{{ event.title }}</div>
      <div class="event-details">
        <div class="event-time">
          <Clock :size="12" />
          {{ event.time }}
        </div>
        <div class="event-date">{{ formatDate(event.date) }}</div>
      </div>
      <div class="event-venue">
        <MapPin :size="12" />
        {{ event.venue }}
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { Clock, MapPin } from 'lucide-vue-next';
import { computed } from 'vue';

interface JoinedEvent {
  id: string;
  title: string;
  date: string;
  time: string;
  venue: string;
  sport: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  image: string;
}

interface Props {
  event: JoinedEvent;
}

const props = defineProps<Props>();

defineEmits<{
  click: [];
}>();

const statusText = computed(() => {
  switch (props.event.status) {
    case 'confirmed':
      return 'Confirmed';
    case 'pending':
      return 'Pending';
    case 'cancelled':
      return 'Cancelled';
    default:
      return '';
  }
});

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow';
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  }
};
</script>

<style lang="scss" scoped>
.joined-event-card {
  min-width: 200px;
  width: 200px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .card-image {
    position: relative;
    height: 80px;
    
    .event-image {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
    
    .status-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
      
      &.status-confirmed {
        background: #10b981;
        color: white;
      }
      
      &.status-pending {
        background: #f59e0b;
        color: white;
      }
      
      &.status-cancelled {
        background: #ef4444;
        color: white;
      }
    }
  }
  
  .card-content {
    padding: 12px;
    
    .event-title {
      font-size: 14px;
      font-weight: 600;
      color: #212529;
      margin-bottom: 8px;
      line-height: 1.2;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .event-details {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 6px;
      
      .event-time,
      .event-date {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        color: #6c757d;
      }
    }
    
    .event-venue {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 11px;
      color: #6c757d;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

@media (max-width: 480px) {
  .joined-event-card {
    min-width: 180px;
    width: 180px;
    
    .card-image {
      height: 70px;
    }
    
    .card-content {
      padding: 10px;
      
      .event-title {
        font-size: 13px;
      }
      
      .event-details,
      .event-venue {
        font-size: 10px;
      }
    }
  }
}
</style>
