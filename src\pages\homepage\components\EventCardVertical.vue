<template>
  <CardVertical
    :image-url="event.photoUrl"
    :image-alt="event.title"
    @click="$emit('click')"
  >
    <template #badges>
      <div v-if="event.badge" class="badges-container">
        <div
          class="status-badge"
          :style="getBadgeStyles(event.badge)"
        >
          {{ event.badge }}
        </div>
      </div>
    </template>

    <template #favorite>
      <q-btn
        v-if="showFavorite"
        round
        flat
        dense
        size="sm"
        class="favorite-btn"
        @click.stop="handleFavoriteClick"
      >
        <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="16px" color="white" />
      </q-btn>
    </template>

    <template #header>
      <div class="event-title">{{ event.title }}</div>
    </template>
    
    <template #price>
      <div class="price-row" v-if="formattedPriceDisplay && (formattedPriceDisplay.current || formattedPriceDisplay.original)">
        <div class="price-section">
          <span v-if="formattedPriceDisplay.original" class="original-price">{{ formattedPriceDisplay.original }}</span>
          <span class="current-price">{{ formattedPriceDisplay.current }}</span>
        </div>
        <div class="rating" v-if="event.rating">
          <q-icon name="star" color="amber" size="14px" class="q-mr-xs" />
          <span>{{ event.rating }}</span>
        </div>
      </div>
    </template>
    
    <template #meta>
      <div class="datetime-row" v-if="formattedDateTime">
        <q-icon name="access_time" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ formattedDateTime }}</span>
      </div>

      <div class="location-row" v-if="event.location">
        <q-icon name="location_on" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ event.location }}</span>
        <span class="distance-chip" v-if="event.distance">{{ event.distance }}</span>
      </div>

      <div class="participants-row" v-if="participantsText">
        <q-icon name="group" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ participantsText }}</span>
        <span class="status-text" v-if="isAlmostFull">Sắp đầy!</span>
      </div>
    </template>
    
    <template #actions>
      <q-btn
        v-if="showBookButton"
        color="primary"
        :label="bookBtnText"
        class="full-width"
        @click.stop="$emit('book')"
      />
    </template>
  </CardVertical>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import CardVertical from 'src/components/CardVertical.vue';
import { useCard } from 'src/composables/useCard';
import type { Badge } from 'src/components/BaseCard.vue';

interface Props {
  event: {
    id: string | number;
    title: string;
    photoUrl?: string;
    sport: string;
    badge?: string;
    originalPrice?: number;
    price: number;
    rating?: number;
    location: string;
    distance?: string;
    date: string;
    startTime: string;
    endTime?: string;
    currentParticipants?: number;
    maxParticipants?: number;
  };

  // UI Controls
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  photoUrl: '/images/image_default.jpg',
  badges: () => [],
  showFavorite: true,
  showBookButton: true,
  bookBtnText: 'Đặt ngay',
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

// Use shared card logic
const {
  isFavorite,
  toggleFavorite,
  formattedDateTime,
  participantsText,
  formattedPriceDisplay
} = useCard({
  originalPrice: props.event.originalPrice,
  price: props.event.price,
  date: props.event.date,
  startTime: props.event.startTime,
  endTime: props.event.endTime,
  currentParticipants: props.event.currentParticipants,
  maxParticipants: props.event.maxParticipants,
  rating: props.event.rating,
  defaultFavorite: props.defaultFavorite
});

const handleFavoriteClick = (event: Event) => {
  const newFavoriteState = toggleFavorite(event);
  emit('favorite', newFavoriteState);
};

const getBadgeStyles = (badge: string) => {
  // Determine badge color based on content
  let color = '#f44336'; // default red
  if (badge.includes('ngày')) color = '#ff9800'; // orange for time-based
  if (badge.includes('Sắp')) color = '#4caf50'; // green for status

  return {
    backgroundColor: color,
    color: 'white'
  };
};

const isAlmostFull = computed(() => {
  if (!props.event.currentParticipants || !props.event.maxParticipants) return false;
  return props.event.currentParticipants / props.event.maxParticipants >= 0.8;
});
</script>

<style lang="scss" scoped>
.badges-container {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  max-width: calc(100% - 16px);
}

.status-badge {
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.favorite-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.event-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  color: #212529;
  margin-bottom: 4px;
}

.event-subtitle {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  
  .original-price {
    font-size: 13px;
    color: #6c757d;
    text-decoration: line-through;
    margin-right: 6px;
  }
  
  .price {
    font-size: 16px;
    font-weight: 700;
    color: #ff8142;
  }
  
  .rating {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
  }
}

.datetime-row,
.location-row,
.participants-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6c757d;
  
  .distance-chip {
    margin-left: auto;
    font-size: 11px;
    background: #f1f5f9;
    padding: 2px 4px;
    border-radius: 4px;
  }
  
  .status-text {
    margin-left: auto;
    font-size: 11px;
    color: #e74c3c;
    font-weight: 500;
  }
}
</style>
