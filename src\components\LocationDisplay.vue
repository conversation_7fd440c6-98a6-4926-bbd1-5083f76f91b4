<template>
  <div class="location-display" @click="handleLocationClick">
    <div class="location-label">Location</div>
    <div class="location-value">
      <MapPin :size="18" class="location-icon" />
      <span class="location-text">{{ displayLocation }}</span>
      <ChevronDown :size="16" class="dropdown-icon" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronDown, MapPin } from 'lucide-vue-next';
import { useLocalStorage } from '@vueuse/core';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { computed } from 'vue';

type Location = {
  latitude?: number | null;
  longitude?: number | null;
};

// Location state
const locationStorage = useLocalStorage<Location>(LOCAL_STORAGE_KEYS.LOCATION, {
  latitude: null,
  longitude: null,
});

// Computed display location
const displayLocation = computed(() => {
  if (locationStorage.value.latitude && locationStorage.value.longitude) {
    // For now, show a default location until we implement reverse geocoding
    // This could be enhanced later with actual address lookup
    return 'New York, USA';
  }

  return 'New York, USA'; // Default location as per design
});

// Handle location click
const handleLocationClick = () => {
  // TODO: Open location picker or navigate to location settings
  console.log('Location clicked - open location picker');
};
</script>

<style lang="scss" scoped>
$primary-color: #ff8142;
$text-primary: #212529;
$text-secondary: #6c757d;

.location-display {
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 200px;

  &:hover {
    opacity: 0.8;
  }

  .location-label {
    font-size: 14px;
    color: $text-secondary;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.2;
  }

  .location-value {
    display: flex;
    align-items: center;
    gap: 6px;

    .location-icon {
      color: $primary-color;
      flex-shrink: 0;
    }

    .location-text {
      font-size: 16px;
      font-weight: 600;
      color: $text-primary;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }

    .dropdown-icon {
      color: $text-secondary;
      flex-shrink: 0;
    }
  }
}

// Mobile responsive
@media (max-width: 600px) {
  .location-display {
    max-width: 160px;

    .location-label {
      font-size: 13px;
    }

    .location-value {
      .location-text {
        font-size: 15px;
      }
    }
  }
}

@media (max-width: 480px) {
  .location-display {
    max-width: 140px;

    .location-label {
      font-size: 12px;
    }

    .location-value {
      .location-text {
        font-size: 14px;
      }
    }
  }
}
</style>
