<template>
  <q-card class="event-card-vertical" @click="$emit('click')">
    <!-- Image Section -->
    <div class="card-image">
      <q-img
        :src="photoUrl || '/images/image_default.jpg'"
        :alt="title"
        class="event-image"
        loading="lazy"
      >
        <!-- Status Badge Slot -->
        <slot name="badge">
          <div v-if="badge" class="status-badge" :style="badgeStyles">
            {{ badge }}
          </div>
        </slot>
        
        <!-- Action Buttons Slot -->
        <slot name="actions">
          <q-btn
            v-if="showFavorite"
            round
            flat
            dense
            class="favorite-btn absolute-top-right"
            @click.stop="toggleFavorite"
          >
            <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="18px" color="white" />
          </q-btn>
        </slot>
      </q-img>
    </div>
    
    <!-- Content Section -->
    <q-card-section class="card-content q-pa-sm">
      <!-- Title Slot -->
      <slot name="title">
        <div class="event-title">{{ title }}</div>
      </slot>
      
      <!-- Price Slot -->
      <slot name="price">
        <div class="price-row" v-if="showPrice">
          <div v-if="originalPrice" class="original-price">{{ formatPrice(originalPrice) }}</div>
          <div class="price">{{ formatPrice(price) }}</div>
          <div class="rating" v-if="rating">
            <q-icon name="star" size="12px" color="amber" />
            {{ rating }}
          </div>
        </div>
      </slot>
      
      <!-- Details Slot -->
      <slot name="details">
        <div class="event-details">
          <slot name="time">
            <div class="detail-row" v-if="date">
              <q-icon name="access_time" size="12px" color="grey-6" />
              <span class="detail-text">{{ formattedDateTime }}</span>
            </div>
          </slot>
          
          <slot name="location">
            <div class="detail-row" v-if="location">
              <q-icon name="location_on" size="12px" color="grey-6" />
              <span class="detail-text">{{ location }}</span>
              <span class="distance" v-if="distance">{{ distance }}</span>
            </div>
          </slot>
          
          <slot name="participants">
            <div class="detail-row" v-if="currentParticipants !== undefined && maxParticipants !== undefined">
              <q-icon name="group" size="12px" color="grey-6" />
              <span class="detail-text">{{ currentParticipants }}/{{ maxParticipants }}</span>
            </div>
          </slot>
          
          <!-- Additional details can be added through this slot -->
          <slot name="additional-details"></slot>
        </div>
      </slot>
    </q-card-section>
    
    <!-- Footer Section -->
    <slot name="footer">
      <q-card-actions v-if="showBookButton" class="q-px-sm q-pb-sm">
        <q-btn
          color="primary"
          class="full-width book-btn"
          :label="bookBtnText || 'Đặt ngay'"
          @click.stop="$emit('book')"
        />
      </q-card-actions>
    </slot>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface Props {
  // Basic info
  title: string;
  photoUrl?: string | undefined;
  
  // Badge
  badge?: string | undefined;
  badgeColor?: string;
  badgeTextColor?: string;
  
  // Price and rating
  originalPrice?: number | undefined;
  price?: number | undefined;
  rating?: number | undefined;
  
  // Date and time
  date?: string | undefined;
  startTime?: string | undefined;
  endTime?: string | undefined;
  
  // Location
  location?: string | undefined;
  distance?: string | undefined;
  
  // Participants
  currentParticipants?: number | undefined;
  maxParticipants?: number | undefined;
  
  // UI Controls
  showPrice?: boolean;
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  photoUrl: '',
  badgeColor: '#f44336',
  badgeTextColor: 'white',
  showPrice: true,
  showFavorite: true,
  showBookButton: true,
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

const isFavorite = ref(props.defaultFavorite);

const toggleFavorite = (event: Event) => {
  event.stopPropagation();
  isFavorite.value = !isFavorite.value;
  emit('favorite', isFavorite.value);
};

const formatPrice = (price?: number) => {
  if (!price && price !== 0) return '';
  
  if (price === 0) return 'Miễn phí';
  
  if (price >= 1000000) {
    return `${(price / 1000000).toFixed(1)}tr đ`;
  } else if (price >= 1000) {
    return `${Math.round(price / 1000)}K đ`;
  }
  return `${price}đ`;
};

const formattedDateTime = computed(() => {
  if (!props.date || !props.startTime) return '';
  
  const date = new Date(props.date);
  const formattedDate = date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
  
  let timeStr = props.startTime;
  if (props.endTime) {
    timeStr += ` - ${props.endTime}`;
  }
  
  return `${formattedDate} • ${timeStr}`;
});

const badgeStyles = computed(() => {
  return {
    backgroundColor: props.badgeColor,
    color: props.badgeTextColor
  };
});
</script>

<style lang="scss" scoped>
.event-card-vertical {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  
  &:active {
    transform: scale(0.98);
  }
  
  .card-image {
    position: relative;
    
    .event-image {
      height: 150px;
    }
    
    .status-badge {
      position: absolute;
      top: 10px;
      left: 10px;
      font-size: 12px;
      font-weight: 500;
      padding: 4px 8px;
      border-radius: 4px;
    }
    
    .favorite-btn {
      margin: 8px;
      background: rgba(0, 0, 0, 0.4);
    }
  }
  
  .card-content {
    .event-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 1.3;
      color: #212529;
      margin-bottom: 6px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }
    
    .price-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .original-price {
        font-size: 14px;
        color: #6c757d;
        text-decoration: line-through;
        margin-right: 6px;
      }
      
      .price {
        font-size: 16px;
        font-weight: 700;
        color: #ff8142;
        margin-right: auto;
      }
      
      .rating {
        display: flex;
        align-items: center;
        gap: 2px;
        font-size: 12px;
        color: #6c757d;
      }
    }
    
    .event-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .detail-row {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #6c757d;
        
        .detail-text {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .distance {
          margin-left: auto;
          font-size: 11px;
          background: #f1f5f9;
          padding: 2px 4px;
          border-radius: 4px;
        }
      }
    }
  }
  
  .book-btn {
    border-radius: 8px;
    text-transform: none;
    font-weight: 600;
  }
}
</style> 