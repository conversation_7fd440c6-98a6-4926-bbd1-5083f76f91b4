<template>
  <q-card style="width: 100%; max-width: 600px">
    <q-card-section class="row items-center q-pb-none no-border-radius">
      <div class="text-h6"><PERSON>h sách người tham gia</div>
      <q-space />
      <q-btn icon="close" flat round dense v-close-popup @click="emit('hide')" />
    </q-card-section>
    <q-separator class="q-my-md" />
    <q-card-section>
      <q-input
        v-model="searchQuery"
        outlined
        dense
        placeholder="Tìm kiếm người tham gia..."
        class="q-mb-md search-input"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

      <q-scroll-area style="height: 400px">
        <q-list>
          <q-item
            v-for="participant in filteredParticipants"
            :key="participant.id"
            class="q-py-md q-mb-md participant-item text-black text-body1 bg-grey-1"
            style="border-radius: 8px"
          >
            <q-item-section avatar>
              <q-avatar size="48px">
                <img :src="participant.avatar" alt="participant avatar" />
              </q-avatar>
            </q-item-section>

            <q-item-section>
              <q-item-label class="text-weight-bold">{{ participant.name }}</q-item-label>
              <q-item-label caption>{{ participant.phone }}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-btn flat dense label="Liên hệ" color="primary" />
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-card-section>
    <q-separator />
    <q-card-section class="text-center">
      <div class="text-black-4 q-pa-none">
        Danh sách tham gia sự kiện của bạn, liên hệ trực tiếp nếu cần thiết
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface ParticipantType {
  id: number;
  name: string;
  avatar: string;
  phone: string;
}

interface Props {
  participants: ParticipantType[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'hide'): void;
}

const emit = defineEmits<Emits>();

const searchQuery = ref('');

const filteredParticipants = computed(() => {
  if (!searchQuery.value) return props.participants;

  const query = searchQuery.value.toLowerCase();
  return props.participants.filter((participant) => participant.name.toLowerCase().includes(query));
});
</script>

<style lang="scss" scoped>
.participant-item {
  &:hover {
    background-color: #fff7ed;
  }
  ::v-deep(.q-focus-helper) {
    opacity: 0 !important;
  }
}
.search-input {
  ::v-deep(.q-field__control) {
    background-color: #f3f4f6 !important;
    border-radius: 8px;
    &:before {
      border: none !important;
    }
  }
}
</style>
