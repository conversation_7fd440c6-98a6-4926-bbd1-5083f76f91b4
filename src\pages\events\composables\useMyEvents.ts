import { useQuasar } from 'quasar';
import { events as mockEvents, myEventsCategories } from 'src/pages/events/data/mock-data';
import { computed, ref } from 'vue';

export interface Participant {
  id: number;
  name: string;
  avatar: string;
  phone: string;
}

export interface Event {
  id: number;
  title: string;
  startTime: string;
  endTime: string;
  totalParticipants: number;
  participants: Participant[];
  status: 'new' | 'joined' | 'completed';
  skillLevels: string[];
  date: string;
  location: string;
  price: number;
  photoUrl: string;
  isPublic?: boolean;
}

export type EventCategory = {
  id: string;
  name: string;
  numberCategory?: number;
};

export function useMyEvents(searchQuery = ref('')) {
  const $q = useQuasar();

  const events = ref([...mockEvents]);

  const selectedMyEventCategory = ref<string>('new');
  const maxParticipants = 3;

  // Dialog states
  const ratingDialog = ref(false);
  // Participant dialog
  const participantsDialog = ref(false);

  // Current event
  const currentEvent = ref<Event | null>(null);

  // Current event participants
  const currentEventParticipants = ref<Participant[]>([]);
  const currentEventTotalParticipants = ref(0);
  const currentEventTitle = ref('');

  // Computed properties
  const filteredEvents = computed(() => {
    let filtered = events.value;

    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(
        (event) =>
          event.title.toLowerCase().includes(query) || event.location.toLowerCase().includes(query),
      );
    }

    return filtered.filter((event) => event.status === selectedMyEventCategory.value);
  });

  // Utility functions
  const numberEventByStatus = (status: string): number => {
    return events.value.filter((event) => event.status === status).length;
  };

  const formatParticipantNames = (participants: Participant[]): string => {
    const names = participants.map((p) => p.name);
    if (names.length <= 2) {
      return names.join(', ');
    }
    return `${names[0]}, ${names[1]} và ${participants.length - 2} người khác`;
  };

  const getButtonLabel = (status: string): string => {
    switch (status) {
      case 'new':
        return 'Chỉnh sửa';
      case 'completed':
        return 'Đánh giá';
      default:
        return 'Hủy tham gia';
    }
  };

  // Event handlers
  const selectCategory = (categoryId: string): void => {
    selectedMyEventCategory.value =
      selectedMyEventCategory.value === categoryId ? 'new' : categoryId;
  };

  const publicEvent = (eventId: number): void => {
    const eventIndex = events.value.findIndex((event) => event.id === eventId);
    if (eventIndex !== -1) {
      events.value[eventIndex]!.isPublic = !events.value[eventIndex]!.isPublic;

      $q.notify({
        message: `${events.value[eventIndex]?.isPublic ? 'Công khai' : 'Ẩn'} sự kiện ${eventId}`,
        color: 'primary',
      });
    }
  };

  const editEvent = (eventId: number): void => {
    $q.notify({
      message: `Chỉnh sửa sự kiện ${eventId}`,
      color: 'positive',
    });
  };

  const cancelEvent = (eventId: number): void => {
    $q.notify({
      message: `Hủy tham gia sự kiện ${eventId}`,
      color: 'negative',
    });
  };

  const rateEvent = (eventId: number): void => {
    const event = events.value.find((event) => event.id === eventId);
    if (event) {
      currentEvent.value = event as unknown as Event;
      ratingDialog.value = true;
    }
  };

  const handleRatingSubmit = ({ eventId, rating }: { eventId: number; rating: number }): void => {
    $q.notify({
      message: `Đã đánh giá sự kiện ${eventId} với ${rating} sao`,
      color: 'positive',
    });
    ratingDialog.value = false;
  };

  const showParticipantsDialog = (event: Event): void => {
    currentEventParticipants.value = event.participants;
    currentEventTotalParticipants.value = event.totalParticipants;
    currentEventTitle.value = event.title;
    participantsDialog.value = true;
  };

  const navigateToEvent = (eventId: number): void => {
    $q.notify({
      message: `Chuyển hướng đến sự kiện ${eventId}`,
      color: 'primary',
    });
  };

  const handleButtonClick = (event: Event): void => {
    switch (event.status) {
      case 'new':
        return editEvent(event.id);
      case 'completed':
        return rateEvent(event.id);
      default:
        return cancelEvent(event.id);
    }
  };

  return {
    events: events.value,
    selectedMyEventCategory,
    myEventsCategories,
    filteredEvents,
    maxParticipants,
    ratingDialog,
    participantsDialog,
    currentEvent,
    currentEventParticipants,
    currentEventTotalParticipants,
    currentEventTitle,
    numberEventByStatus,
    formatParticipantNames,
    getButtonLabel,
    selectCategory,
    publicEvent,
    editEvent,
    cancelEvent,
    rateEvent,
    handleRatingSubmit,
    showParticipantsDialog,
    navigateToEvent,
    handleButtonClick,
  };
}
