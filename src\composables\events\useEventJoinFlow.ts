import { ref } from 'vue';

// Types
export interface EventJoinFormData {
  skillLevels: string[];
  numberOfSlots: number | null;
  phoneNumber: string;
  organizerPhoneNumber?: string;
  eventId?: string | number;
}

export interface EventJoinData {
  id: string | number;
  title: string;
  price?: string | number;
  date?: string;
  startTime?: string;
  endTime?: string;
  photoUrl?: string;
  maxSlots?: number;
  organizer?: {
    name: string;
    phoneNumber: string;
  };
  organizerPhoneNumber?: string;
}

// Singleton state
const selectedEvent = ref<EventJoinData | null>(null);
const formData = ref<EventJoinFormData | null>(null);
const showJoinDialog = ref(false);
const showConfirmDialog = ref(false);
const showSuccessDialog = ref(false);

export function useEventJoinFlow() {
  const joinEvent = (event: EventJoinData) => {
    selectedEvent.value = event;
    showJoinDialog.value = true;
  };

  const confirmJoin = (data: EventJoinFormData) => {
    formData.value = data;
    showJoinDialog.value = false;
    showConfirmDialog.value = true;
    console.log('custom confirm join', data);
  };

  const finalConfirm = () => {
    showConfirmDialog.value = false;
    showSuccessDialog.value = true;
  };

  const cancelConfirm = () => {
    showConfirmDialog.value = false;
    resetDialogs();
  };

  const completeBooking = () => {
    showSuccessDialog.value = false;
    resetDialogs();
  };

  const resetDialogs = () => {
    showJoinDialog.value = false;
    showConfirmDialog.value = false;
    showSuccessDialog.value = false;
    selectedEvent.value = null;
    formData.value = null;
  };

  return {
    // State
    selectedEvent,
    formData,
    showJoinDialog,
    showConfirmDialog,
    showSuccessDialog,

    // Methods
    joinEvent,
    confirmJoin,
    finalConfirm,
    cancelConfirm,
    completeBooking,
    resetDialogs,
  };
}
