<template>
  <q-card class="base-card" @click="$emit('click')">
    <slot />
  </q-card>
</template>

<script setup lang="ts">
export interface Badge {
  text: string;
  color?: string;
  textColor?: string;
}

interface Props {
  clickable?: boolean;
}

defineProps<Props>();

defineEmits<{
  click: [];
}>();
</script>

<style lang="scss" scoped>
.base-card {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
  cursor: pointer;
  
  &:active {
    transform: scale(0.98);
  }
}

@media (max-width: 599px) {
  .base-card {
    border-radius: 12px;
  }
}
</style>
