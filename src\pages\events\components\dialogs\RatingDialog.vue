<template>
  <q-card style="width: 100%; max-width: 400px">
    <q-card-section class="row items-center q-pb-none">
      <div class="text-h6"><PERSON><PERSON><PERSON> gi<PERSON> hoạt động</div>
      <q-space />
      <q-btn icon="close" flat round dense v-close-popup @click="emit('close')" />
    </q-card-section>
    <q-separator class="q-my-md" />
    <q-card-section>
      <div class="row items-center q-mb-md">
        <q-img
          :src="event?.photoUrl || '/images/image_default.jpg'"
          class="q-mr-md"
          fit="cover"
          width="64px"
          height="64px"
          style="border-radius: 12px"
          s
        />
        <div class="column">
          <div class="text-weight-bold">{{ event?.title }}</div>
          <div class="text-caption text-grey-7">{{ event?.location }}</div>
        </div>
      </div>

      <div class="text-body1 q-mb-md"><PERSON><PERSON><PERSON> cảm thấy hoạt động này như thế nào?</div>

      <div class="row justify-center q-mb-xs">
        <q-rating
          v-model="rating"
          size="32px"
          icon="star"
          icon-selected="star"
          icon-half="star"
          color="grey"
          color-selected="accent"
          iconAriaLabel="star"
          noReset
          :max="5"
        />
      </div>
      <div class="text-body2 text-grey-7 text-center">
        <div v-if="rating === 1" class="text-red">Rất tệ</div>
        <div v-else-if="rating === 2" class="text-black-4">Tệ</div>
        <div v-else-if="rating === 3" class="text-black-4">Bình thường</div>
        <div v-else-if="rating === 4" class="text-black-4">Tốt</div>
        <div v-else-if="rating === 5" class="text-black-4">Rất tốt</div>
        <div v-else class="text-grey-6">Chọn số sao để đánh giá</div>
      </div>
    </q-card-section>

    <q-card-actions class="row no-wrap justify-center q-px-md">
      <q-btn
        class="col-6"
        label="Hủy"
        size="md"
        style="border-radius: 8px; height: 48px"
        color="grey-3"
        text-color="dark"
        v-close-popup
        @click="emit('close')"
      />
      <q-btn
        class="col-6"
        size="md"
        :disable="!rating"
        style="border-radius: 8px; height: 48px"
        color="primary"
        text-color="white"
        label="Gửi đánh giá"
        @click="submitRating"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface EventData {
  id: number;
  title: string;
  location: string;
  photoUrl?: string;
  [key: string]: any;
}

interface Props {
  event?: EventData;
}

interface Emits {
  (e: 'submit', payload: { eventId: number; rating: number }): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const rating = ref(0);

const submitRating = () => {
  if (props.event && rating.value > 0) {
    emit('submit', {
      eventId: props.event.id,
      rating: rating.value,
    });
  }
};
</script>
