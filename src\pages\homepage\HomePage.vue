<template>
  <q-page class="homepage">
    <!-- Loading Skeleton -->
    <HomePageSkeleton v-if="isLoading" />

    <!-- Actual Content -->
    <div v-else>
      <!-- Quick Search & Filter -->
      <section class="search-section q-px-md q-py-md">
        <div class="search-container">
          <q-input
            v-model="searchQuery"
            outlined
            placeholder="Tìm kiếm sự kiện, sân thể thao..."
            class="search-input"
            bg-color="white"
          >
            <template v-slot:prepend>
              <Search :size="20" color="#6c757d" />
            </template>
            <template v-slot:append>
              <q-btn
                flat
                round
                dense
                @click="openFilterDialog"
                class="filter-toggle-btn"
              >
                <SlidersHorizontal :size="18" color="#ff8142" />
              </q-btn>
            </template>
          </q-input>
        </div>
      </section>

      <!-- Content Sections -->
      <div class="page-content q-px-md">
        <!-- Stats Section -->
        <StatsSection class="q-mb-lg" />
        
        <!-- Events Section -->
        <section class="events-section q-mb-lg">
          <!-- Section Header -->
          <div class="section-header q-mb-md">
            <div class="section-title">
              <q-icon name="flash_on" color="primary" size="20px" class="q-mr-xs" />
              <span class="title-text">Sự kiện xế về</span>
              <q-chip color="red" text-color="white" size="sm" class="q-ml-xs">
                Hot
              </q-chip>
            </div>
            <q-btn
              flat
              no-caps
              color="primary"
              class="see-all-btn"
              @click="handleSeeAllEvents"
            >
              Xem tất cả
              <q-icon name="chevron_right" size="16px" class="q-ml-xs" />
            </q-btn>
          </div>

          <!-- Events Scroll Container -->
          <div class="events-scroll-container">
            <div class="events-list">
              <EventCardVertical
                v-for="event in ticketEvents"
                :key="event.id"
                :event="event"
                class="event-card-item"
                @click="viewEventDetails(event)"
                @book="handleEventBook(event)"
                @favorite="handleFavoriteEvent(event, $event)"
              />
            </div>
          </div>
        </section>
        
      </div>

      <!-- Filter Dialog -->
      <q-dialog 
        v-model="showFilterDialog" 
        :full-width="$q.screen.lt.sm"
        :full-height="$q.screen.lt.sm"
        :maximized="$q.screen.lt.sm"
      >
        <FilterDialog
          @apply="applyFilters"
          @close="showFilterDialog = false"
        />
      </q-dialog>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { Search, SlidersHorizontal } from 'lucide-vue-next';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';

// Components
import FilterDialog from 'src/components/FilterDialog.vue';
import HomePageSkeleton from './components/HomePageSkeleton.vue';
import StatsSection from './components/StatsSection.vue';
import EventCardVertical from './components/EventCardVertical.vue';

// Data
import type { Event } from './data/mockData';
import { ticketEventsData } from './data/mockData';

const $q = useQuasar();
const router = useRouter();

// Loading state
const isLoading = ref(true);

// Reactive state
const searchQuery = ref('');
const showFilterDialog = ref(false);

// Mock data
const ticketEvents = ref<Event[]>(ticketEventsData);

// No computed properties needed for now

// Methods
const openFilterDialog = () => {
  showFilterDialog.value = true;
};

const applyFilters = (filters: any) => {
  console.log('Applied filters:', filters);
  showFilterDialog.value = false;
};

const viewEventDetails = (event: Event) => {
  router.push(`/events/${event.id}`);
};

const handleSeeAllEvents = () => {
  router.push('/events');
};

const handleFavoriteEvent = (event: Event, isFavorite: boolean) => {
  console.log('Favorite event:', event.title, isFavorite);
  // TODO: Implement favorite logic
};

const handleEventBook = (event: Event) => {
  console.log('Book event:', event);
  router.push(`/events/${event.id}/book`);
};

// Lifecycle
onMounted(async () => {
  try {
    // Simulate API calls
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch (error) {
    console.error('Error loading homepage data:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
// Design system variables
$primary-color: #ff8142;
$secondary-color: #f8f9fa;
$text-primary: #212529;
$text-secondary: #6c757d;
$border-color: #e9ecef;
$border-radius: 12px;
$shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
$shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);

.homepage {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  min-height: 100vh;
}

/* Search Input Styling */
.search-input :deep(.q-field__control) {
  border-radius: 24px;
  box-shadow: 0 0px 8px rgba(0, 0, 0, 0.12);
  height: 52px;
}

.search-input :deep(.q-field__control):before,
.search-input :deep(.q-field__control):after {
  border: none;
}

.page-content {
  padding-bottom: 24px;
}

// TRENDING SECTION
.trending-section {
  .trending-categories {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    
    .trending-category {
      border-radius: 16px;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      
      &:active {
        transform: scale(0.98);
      }
      
      .category-icon {
        width: 48px;
        height: 48px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
      }
      
      .category-name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 4px;
      }
      
      .growth-rate {
        font-size: 12px;
        color: #10b981;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2px;
      }
    }
  }
}

// VENUES SECTION
.venues-section {
  .venues-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .venue-card {
      border-radius: 16px;
      overflow: hidden;
      
      .venue-image {
        width: 100px;
        height: 100px;
      }
      
      .venue-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 6px;
      }
      
      .venue-rating {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;
        
        .rating-value {
          font-size: 12px;
          font-weight: 600;
          color: #f59e0b;
        }
        
        .rating-count {
          font-size: 12px;
          color: #94a3b8;
        }
        
        .venue-distance {
          margin-left: auto;
          font-size: 12px;
          color: #6c757d;
        }
      }
      
      .venue-location {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 10px;
      }
      
      .venue-amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 12px;
        
        .amenity-chip {
          height: 24px;
        }
      }
      
      .book-btn {
        text-transform: none;
      }
    }
  }
}

// Mobile optimizations
@media (max-width: 599px) {
  .trending-section {
    .trending-categories {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

// Events Section Styles
.events-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      display: flex;
      align-items: center;

      .title-text {
        font-size: 18px;
        font-weight: 600;
        color: $text-primary;
      }
    }

    .see-all-btn {
      font-size: 14px;
      font-weight: 500;
      padding: 4px 8px;
    }
  }

  .events-scroll-container {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .events-list {
    display: flex;
    gap: 16px;
    scroll-snap-type: x mandatory;

    .event-card-item {
      flex: 0 0 280px;
      scroll-snap-align: start;
    }
  }
}

// Mobile optimizations
@media (max-width: 599px) {
  .events-section {
    .section-header {
      .section-title .title-text {
        font-size: 16px;
      }

      .see-all-btn {
        font-size: 13px;
      }
    }

    .events-list {
      gap: 12px;

      .event-card-item {
        flex: 0 0 260px;
      }
    }
  }
}
</style>
