import { ActivitiesService } from 'src/services/activitiesService';
import type { ISport } from 'src/types/activities';
import { onMounted, ref } from 'vue';

export const useSportList = () => {
  const sportList = ref<ISport[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const activeSportId = ref<string>('1');

  const setActiveSport = (sportId: string) => {
    activeSportId.value = activeSportId.value === sportId ? '1' : sportId;
    console.log(activeSportId.value);
  };

  const loadSportList = async () => {
    loading.value = true;
    const response = await ActivitiesService.getSports();
    sportList.value = response.data.sports;
  };

  onMounted(async () => {
    await loadSportList();
  });

  return {
    // State
    sportList,
    loading,
    error,
    activeSportId,
    // Methods
    setActiveSport,
    loadSportList,
  };
};
