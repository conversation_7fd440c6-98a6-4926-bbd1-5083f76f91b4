<template>
  <q-layout view="hHh lpR fFf" class="bg-grey-2 q-border">
    <q-header class="bg-white text-dark q-py-sm" bordered>
      <q-toolbar class="main-header">
        <q-toolbar-title>
          <LocationDisplay />
        </q-toolbar-title>

        <q-btn
          dense
          flat
          round
          @click="toggleRightDrawer"
          class="notification-btn"
        >
          <Bell :size="20" />
          <q-badge
            v-if="hasNotifications"
            color="red"
            floating
            rounded
            class="notification-badge"
          >
            {{ notificationCount }}
          </q-badge>
        </q-btn>
      </q-toolbar>
    </q-header>
    <q-drawer v-model="rightDrawerOpen" side="right" behavior="mobile" elevated>
      <!-- drawer content -->
    </q-drawer>

    <q-page-container class="q-page-container">
      <router-view />
    </q-page-container>

    <!-- Event Join Dialog Components -->
    <EventJoinDialog
      v-if="joinFlow.selectedEvent"
      v-model="showJoinDialog"
      :event="unref(joinFlow.selectedEvent)"
      @confirm="joinFlow.confirmJoin"
      @cancel="joinFlow.resetDialogs"
    />

    <ConfirmDialog
      v-model="showConfirmDialog"
      :phone-number="unref(joinFlow.formData)?.organizerPhoneNumber || ''"
      @confirm="joinFlow.finalConfirm"
      @cancel="joinFlow.cancelConfirm"
    />

    <SuccessDialog v-model="showSuccessDialog" @confirm="joinFlow.completeBooking" />

    <!-- Bottom Navigation -->
    <q-footer class="bg-white text-dark" bordered>
      <q-tabs
        v-model="currentTab"
        class="text-grey-6 main-navigation"
        indicator-color="transparent"
        align="justify"
        no-caps
      >
        <q-route-tab
          v-for="item in navigationItems"
          :key="item.name"
          :name="item.name"
          :to="item.path"
          class="bottom-nav-tab"
        >
          <template v-slot:default>
            <div class="column items-center">
              <component :is="mapIcon(item.icon)" :size="24" />
              <div class="q-tab__label">{{ t(item.labelKey) }}</div>
            </div>
          </template>
        </q-route-tab>
      </q-tabs>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { Bell, BookmarkCheck, CalendarCheck2, CircleUser, House, Orbit } from 'lucide-vue-next';
import LocationDisplay from 'src/components/LocationDisplay.vue';
import { useEventJoinFlow } from 'src/composables/events/useEventJoinFlow';
import { NAVIGATION_ITEMS } from 'src/constants';
import ConfirmDialog from 'src/pages/events/components/dialogs/ConfirmDialog.vue';
import EventJoinDialog from 'src/components/EventJoinDialog.vue';
import SuccessDialog from 'src/pages/events/components/dialogs/SuccessDialog.vue';
import { computed, provide, ref, unref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

const { t } = useI18n();
const route = useRoute();
const rightDrawerOpen = ref(false);
const navigationItems = NAVIGATION_ITEMS;

// Notification state
const hasNotifications = ref(true); // Mock notification state
const notificationCount = ref(3); // Mock notification count
// Create the join flow instance
const joinFlow = useEventJoinFlow();

// Dialog state using computed with getter/setter
const showJoinDialog = computed({
  get: () => joinFlow.showJoinDialog.value,
  set: (value) => {
    joinFlow.showJoinDialog.value = value;
  },
});

const showConfirmDialog = computed({
  get: () => joinFlow.showConfirmDialog.value,
  set: (value) => {
    joinFlow.showConfirmDialog.value = value;
  },
});

const showSuccessDialog = computed({
  get: () => joinFlow.showSuccessDialog.value,
  set: (value) => {
    joinFlow.showSuccessDialog.value = value;
  },
});

// Provide the join flow to all child components
provide('eventJoinFlow', joinFlow);

const mapIcon = (icon: string) => {
  switch (icon) {
    case 'home':
      return House;
    case 'events':
      return CalendarCheck2;
    case 'venues':
      return Orbit;
    case 'bookings':
      return BookmarkCheck;
    case 'profile':
      return CircleUser;
    default:
      return House;
  }
};

// Current tab based on route
const currentTab = computed(() => {
  const currentPath = route.path;
  const activeItem = navigationItems.find((item) => item.path === currentPath);
  return activeItem?.name || 'home';
});

const toggleRightDrawer = () => {
  rightDrawerOpen.value = !rightDrawerOpen.value;
};
</script>

<style lang="scss" scoped>
.q-page-container {
  max-width: 690px;
  margin: 0 auto;
  border-left: 1px solid #eeeeee;
  border-right: 1px solid #eeeeee;
}

.main-header {
  max-width: 690px;
  margin: 0 auto;

  .notification-btn {
    position: relative;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    color: #212529;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
      transform: scale(1.05);
    }

    .notification-badge {
      font-size: 10px;
      font-weight: 600;
      min-width: 16px;
      height: 16px;
      top: 6px;
      right: 6px;
    }
  }
}

.main-navigation {
  max-width: 690px;
  margin: 0 auto;
}

.bottom-nav-tab {
  min-height: 60px;
  padding: 8px 12px;

  // Default inactive state styling
  color: #9e9e9e !important;

  :deep(.q-tab__content) {
    flex-direction: column;
    gap: 4px;
  }

  :deep(.q-tab__icon) {
    font-size: 24px;
    color: #9e9e9e !important;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.q-tab__label) {
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    color: #9e9e9e !important;
  }

  // Active state styling
  &.q-tab--active {
    color: #ff8142 !important;

    :deep(.q-tab__icon) {
      color: #ff8142 !important;
    }

    :deep(.q-tab__label) {
      color: #ff8142 !important;
      font-weight: 600;
    }
  }
}
</style>
