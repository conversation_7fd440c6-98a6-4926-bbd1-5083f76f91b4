<template>
  <BaseCard @click="$emit('click')">
    <!-- Image with Overlay Content -->
    <div class="overlay-container">
      <slot name="image">
        <q-img
          :src="imageUrl"
          :alt="imageAlt"
          class="overlay-image"
          loading="lazy"
        >
          <slot name="badges" />
          <slot name="favorite" />
          
          <!-- Overlay Content -->
          <div class="overlay-content">
            <slot name="overlay-header" />
            <slot name="overlay-meta" />
            <slot name="overlay-price" />
          </div>
        </q-img>
      </slot>
    </div>

    <!-- Bottom Section -->
    <div class="bottom-section">
      <slot name="bottom-content" />
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import BaseCard from './BaseCard.vue';

interface Props {
  imageUrl?: string;
  imageAlt?: string;
}

withDefaults(defineProps<Props>(), {
  imageUrl: '/images/image_default.jpg',
  imageAlt: 'Card image'
});

defineEmits<{
  click: [];
}>();
</script>

<style lang="scss" scoped>
.overlay-container {
  position: relative;
  
  .overlay-image {
    width: 100%;
    height: 200px;
    position: relative;
    
    .overlay-content {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      padding: 40px 16px 16px;
      color: white;
    }
  }
}

.bottom-section {
  background: white;
  border-radius: 0 0 16px 16px;
}

@media (max-width: 599px) {
  .overlay-container .overlay-image {
    height: 160px;
    
    .overlay-content {
      padding: 30px 12px 12px;
    }
  }
}
</style>
