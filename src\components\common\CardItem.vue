<script setup lang="ts">
defineProps<{
  cardProps?: {
    containerClass?: string;
    hasCustomShadow?: boolean;
    flat?: boolean;
    square?: boolean;
    bordered?: boolean;
    dark?: boolean;
    tag?: string;
    color?: string;
    textColor?: string;
  };
}>();
</script>

<template>
  <q-card
    :class="cardProps?.containerClass"
    v-bind="cardProps"
    :style="{
      boxShadow: cardProps?.hasCustomShadow
        ? 'rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 1px 2px 0px !important'
        : 'none',
    }"
  >
    <slot />
  </q-card>
</template>
