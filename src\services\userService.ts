import { api } from 'src/boot/axios';
import type { IProfileResponse, IUpdateProfileRequest } from 'src/types/auth';

export const userService = {
  // Get current user profile
  getProfile: async (): Promise<IProfileResponse> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  // Update user profile
  updateProfile: async (data: IUpdateProfileRequest): Promise<IProfileResponse> => {
    const response = await api.put('/auth/me', data);
    return response.data;
  },
};
