<template>
  <DialogWrapper
    title="Báo cáo tin đăng"
    :show-header="true"
    :show-footer="true"
    :show-close-button="true"
    :min-width="$q.screen.lt.sm ? '100%' : '500px'"
    max-height="90vh"
    :full-height="false"
    @close="$emit('close')"
  >
    <template #content>
      <!-- Report Options -->
      <q-card-section class="q-pa-md">
        <div class="text-subtitle2 text-weight-medium q-mb-md text-grey-7">
          Chọn lý do báo cáo
          <q-icon name="info" size="20px" class="cursor-pointer">
            <q-tooltip
              class="bg-grey-8 text-white text-body2"
              anchor="bottom left"
              self="top left"
              :offset="[10, 10]"
              max-width="250px"
              icon="info"
            >
              Những báo cáo vi phạm được chúng tôi xem xét trong tối đa 24 giờ xác định
              xem họ có vi phạm nguyên tắc không. Tin đăng vi phạm sẽ bị yêu cầu chỉnh sửa 
              hoặc xóa bỏ. T<PERSON><PERSON> mức độ vi phạm, người đăng tin có thể bị khóa tài khoản vĩnh viễn.
            </q-tooltip>
          </q-icon>
        </div>
        <div class="report-options">
          <q-list class="rounded-borders">
            <q-item
              v-for="(option, index) in reportOptions"
              :key="option.id"
              clickable
              v-ripple
              @click="selectOption(option.id)"
              class="report-option-item"
              :class="{ 'bg-orange-1': selectedOption === option.id }"
            >
              <q-item-section side>
                <q-radio
                  :model-value="selectedOption"
                  :val="option.id"
                  color="primary"
                  @update:model-value="selectOption"
                  size="md"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-body1 text-weight-medium"
                  :class="$q.screen.lt.sm ? 'text-body2' : 'text-body1'"
                >
                  {{ option.label }}
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon
                  name="help_outline"
                  size="20px"
                  color="grey-5"
                  class="cursor-pointer"
                >
                  <q-tooltip
                    class="bg-grey-8 text-white text-body2"
                    anchor="center left"
                    self="center right"
                    :offset="[10, 10]"
                    max-width="200px"
                  >
                    {{ option.tooltip }}
                  </q-tooltip>
                </q-icon>
              </q-item-section>
              <q-separator v-if="index < reportOptions.length - 1" />
            </q-item>
          </q-list>
        </div>
      </q-card-section>

      <!-- Additional Details (Optional) -->
      <q-card-section class="q-px-md q-py-none">
        <div class="text-subtitle2 text-weight-medium q-mb-sm text-grey-7">
          Chi tiết bổ sung (tùy chọn)
        </div>
        <q-input
          v-model="additionalDetails"
          type="textarea"
          outlined
          placeholder="Mô tả chi tiết về vấn đề..."
          maxlength="500"
          rows="4"
        />
      </q-card-section>
    </template>

    <template #footer>
      <div class="row full-width no-wrap q-col-gutter-sm">
        <div class="col-6">
          <q-btn
            class="full-width"
            outline
            label="Hủy"
            color="grey-7"
            @click="$emit('close')"
            no-caps
            :size="$q.screen.lt.sm ? 'md' : 'lg'"
          />
        </div>
        <div class="col-6">
          <q-btn
            class="full-width"
            unelevated
            label="Gửi báo cáo"
            color="primary"
            :disable="!selectedOption"
            @click="submitReport"
            no-caps
            :size="$q.screen.lt.sm ? 'md' : 'lg'"
          />
        </div>
      </div>
    </template>
  </DialogWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import DialogWrapper from './DialogWrapper/DialogWrapper.vue';

// Types
interface ReportOption {
  id: string;
  label: string;
  tooltip: string;
}

// Props & Emits
interface Props {
  targetId?: string | number;
  targetType?: 'event' | 'user' | 'venue';
}

const props = withDefaults(defineProps<Props>(), {
  targetType: 'event'
});

const emit = defineEmits<{
  close: [];
  submit: [data: { option: string; details: string; targetId?: string | number | undefined; targetType: string }];
}>();

// Composables
const $q = useQuasar();

// Reactive data
const selectedOption = ref<string>('');
const additionalDetails = ref<string>('');



// Report options based on the image provided
const reportOptions: ReportOption[] = [
  {
    id: 'inappropriate_content',
    label: 'Đã đủ thành viên',
    tooltip: 'Người đăng tin quên chưa cập nhật trạng thái đã đủ thành viên'
  },
  {
    id: 'incorrect_info',
    label: 'Mô tả không chính xác',
    tooltip: 'Thông tin được cung cấp không chính xác hoặc gây hiểu lầm'
  },
  {
    id: 'inappropriate_contact',
    label: 'Không thể liên hệ',
    tooltip: 'Không thể liên hệ được với người đăng tin'
  },
  {
    id: 'inappropriate_post',
    label: 'Bài viết không phù hợp',
    tooltip: 'Bài viết vi phạm nguyên tắc cộng đồng'
  },
  {
    id: 'spam',
    label: 'Spam',
    tooltip: 'Tin đăng spam hoặc quảng cáo không mong muốn'
  },
  {
    id: 'other_violation',
    label: 'Lý do khác',
    tooltip: 'Vi phạm khác không được liệt kê ở trên'
  }
];

// Methods
const selectOption = (optionId: string) => {
  selectedOption.value = optionId;
};

const submitReport = () => {
  if (!selectedOption.value) {
    $q.notify({
      type: 'warning',
      message: 'Vui lòng chọn lý do báo cáo',
      position: 'top'
    });
    return;
  }

  const reportData = {
    option: selectedOption.value,
    details: additionalDetails.value.trim(),
    targetId: props.targetId,
    targetType: props.targetType
  };

  emit('submit', reportData);

  // Show success notification
  $q.notify({
    type: 'positive',
    message: 'Báo cáo đã được gửi thành công',
    position: 'top',
    timeout: 3000
  });

  // Close dialog
  emit('close');
};
</script>

<style lang="scss" scoped>
// Report-specific styling
.report-option-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 129, 66, 0.05);
  }

  .q-item__section--side {
    padding-right: 12px;
  }
}

.additional-details-input {
  :deep(.q-field__control) {
    border-radius: 8px;
  }
}

// Mobile optimizations
@media (max-width: $breakpoint-xs-max) {
  .report-option-item {
    padding: 4px;
  }
}
</style>
