export const AUTH_ROUTES = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  VERIFY_EMAIL: '/auth/verify-email',
  RESET_PASSWORD: '/auth/reset-password',
};

export const LOCAL_STORAGE_KEYS = {
  IS_ONBOARDED: 'is_onboarded',
  INTRO_SEEN: 'introSeen',
  USER_DATA: 'userData',
  TOKEN: 'token',
  REFRESH_TOKEN: 'refreshToken',
  EXPIRES_AT: 'expiresAt',
  ID_TOKEN: 'idToken',
  USER_ID: 'userId',
  USER_NAME: 'userName',
  AUTH_INFORMATION: 'authInformation',
  LOCATION: 'location',
  PROFILE_SETUP: 'profileSetup',
  USER_LANGUAGE: 'userLanguage',
};

// Navigation constants
export type NavigationItem = {
  name: string;
  path: string;
  icon: string;
  label: string;
  labelKey: string;
};

export const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    name: 'home',
    path: '/home',
    icon: 'home',
    label: 'Trang chủ',
    labelKey: 'navigation.home',
  },
  {
    name: 'events',
    path: '/events',
    icon: 'events',
    label: 'Sự kiện',
    labelKey: 'navigation.events',
  },
  {
    name: 'venues',
    path: '/venues',
    icon: 'venues',
    label: 'Sân',
    labelKey: 'navigation.venues',
  },
  {
    name: 'bookings',
    path: '/bookings',
    icon: 'bookings',
    label: 'Đặt chỗ của tôi',
    labelKey: 'navigation.bookings',
  },
  {
    name: 'profile',
    path: '/profile',
    icon: 'profile',
    label: 'Hồ sơ',
    labelKey: 'navigation.profile',
  },
];
