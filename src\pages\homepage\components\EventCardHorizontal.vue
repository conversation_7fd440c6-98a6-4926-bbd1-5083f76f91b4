<template>
  <CardHorizontal
    :image-url="imageUrl"
    :image-alt="event.title"
    @click="$emit('click')"
  >
    <template #badges>
      <div v-if="event.badge" class="badges-container">
        <div
          class="status-badge"
          :style="getBadgeStyles(event.badge)"
        >
          {{ event.badge }}
        </div>
      </div>
    </template>

    <template #header>
      <div class="event-header">
        <div class="event-title">{{ event.title }}</div>
        <div class="event-tags">
          <q-chip
            v-for="tag in eventTags"
            :key="tag.text"
            :color="tag.color"
            :text-color="tag.textColor"
            size="sm"
            class="event-tag"
          >
            {{ tag.text }}
          </q-chip>
        </div>
      </div>
    </template>

    <template #price>
      <div class="price-section">
        <div class="price-info">
          <span v-if="formattedPriceDisplay && formattedPriceDisplay.original" class="original-price">
            {{ formattedPriceDisplay.original }}
          </span>
          <span class="current-price">
            {{ formattedPriceDisplay?.current || formatPrice(event.price) }}
          </span>
        </div>
        <div class="rating-info" v-if="event.rating">
          <q-icon name="star" color="amber" size="14px" class="q-mr-xs" />
          <span>{{ event.rating }} ({{ ratingCount }})</span>
        </div>
      </div>
    </template>

    <template #meta>
      <div class="meta-info">
        <div class="datetime-info" v-if="formattedDateTime">
          <q-icon name="schedule" size="14px" color="grey-6" class="q-mr-xs" />
          <span>{{ formattedDateTime }}</span>
        </div>

        <div class="location-info" v-if="event.location">
          <q-icon name="place" size="14px" color="grey-6" class="q-mr-xs" />
          <span>{{ event.location }} ({{ event.distance }})</span>
        </div>

        <div class="organizer-info">
          <q-avatar size="20px" class="q-mr-xs">
            <img :src="organizerAvatar" :alt="organizerName" />
          </q-avatar>
          <span class="organizer-name">{{ organizerName }}</span>
          <q-icon name="verified" size="14px" color="blue" class="q-ml-xs" v-if="isVerified" />
          <span class="participant-count">{{ participantText }}</span>
        </div>
      </div>
    </template>

    <template #actions>
      <div class="action-section">
        <q-btn
          v-if="showFavorite"
          flat
          round
          dense
          size="sm"
          @click.stop="handleFavoriteClick"
          class="favorite-btn"
        >
          <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="16px" color="grey-6" />
        </q-btn>
        <q-btn
          v-if="showBookButton"
          color="primary"
          :label="bookBtnText"
          size="sm"
          class="book-btn"
          @click.stop="$emit('book')"
        />
      </div>
    </template>
  </CardHorizontal>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import CardHorizontal from 'src/components/CardHorizontal.vue';
import { useCard } from 'src/composables/useCard';

interface Props {
  event: {
    id: string | number;
    title: string;
    photoUrl?: string;
    sport: string;
    badge?: string;
    originalPrice?: number;
    price: number;
    rating?: number;
    location: string;
    distance?: string;
    date: string;
    startTime: string;
    endTime?: string;
    currentParticipants?: number;
    maxParticipants?: number;
  };

  // UI Controls
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showFavorite: true,
  showBookButton: true,
  bookBtnText: 'Đặt chỗ',
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

// Use shared card logic
const {
  isFavorite,
  toggleFavorite,
  formattedDateTime,
  participantsText,
  formattedPriceDisplay,
  formatPrice
} = useCard({
  originalPrice: props.event.originalPrice,
  price: props.event.price,
  date: props.event.date,
  startTime: props.event.startTime,
  endTime: props.event.endTime,
  currentParticipants: props.event.currentParticipants,
  maxParticipants: props.event.maxParticipants,
  rating: props.event.rating,
  defaultFavorite: props.defaultFavorite
});

// Computed properties
const imageUrl = computed(() => {
  return props.event.photoUrl || '/images/image_default.jpg';
});

const eventTags = computed(() => {
  const tags: Array<{ text: string; color: string; textColor: string }> = [];

  // Sport tag
  if (props.event.sport === 'football') {
    tags.push({ text: 'Bóng đá', color: 'blue', textColor: 'white' });
  } else if (props.event.sport === 'yoga') {
    tags.push({ text: 'Yoga', color: 'purple', textColor: 'white' });
  } else if (props.event.sport === 'tennis') {
    tags.push({ text: 'Tennis', color: 'green', textColor: 'white' });
  } else if (props.event.sport === 'running') {
    tags.push({ text: 'Chạy bộ', color: 'orange', textColor: 'white' });
  }

  // Level tags based on price
  if (props.event.price === 0) {
    tags.push({ text: 'Miễn phí', color: 'green', textColor: 'white' });
  } else if (props.event.originalPrice && props.event.originalPrice > props.event.price) {
    tags.push({ text: 'Giảm giá', color: 'red', textColor: 'white' });
  }

  return tags;
});

const organizerName = computed(() => {
  // Mock organizer names based on sport
  const organizers = {
    football: 'Nguyễn Văn A',
    yoga: 'Yoga Studio HN',
    tennis: 'Tennis Pro',
    running: 'Running Club HN'
  };
  return organizers[props.event.sport as keyof typeof organizers] || 'Organizer';
});

const organizerAvatar = computed(() => {
  return '/images/avatar_default.jpg';
});

const isVerified = computed(() => {
  return ['tennis', 'yoga'].includes(props.event.sport);
});

const ratingCount = computed(() => {
  // Mock rating counts
  const counts = { football: 34, yoga: 67, tennis: 28, running: 45 };
  return counts[props.event.sport as keyof typeof counts] || 20;
});

const participantText = computed(() => {
  if (!props.event.currentParticipants || !props.event.maxParticipants) {
    return '';
  }

  const remaining = props.event.maxParticipants - props.event.currentParticipants;
  if (remaining <= 1) {
    return 'Còn 1 chỗ';
  } else if (remaining <= 5) {
    return `Còn ${remaining} chỗ`;
  }

  return `${props.event.currentParticipants}/${props.event.maxParticipants}`;
});

const getBadgeStyles = (badge: string) => {
  let color = '#f44336'; // default red
  if (badge.includes('ngày')) color = '#ff9800';
  if (badge.includes('Sắp')) color = '#4caf50';

  return {
    backgroundColor: color,
    color: 'white'
  };
};

const handleFavoriteClick = (event: Event) => {
  const newFavoriteState = toggleFavorite(event);
  emit('favorite', newFavoriteState);
};
</script>

<style lang="scss" scoped>
.badges-container {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 1;
}

.status-badge {
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  white-space: nowrap;
}

.event-header {
  margin-bottom: 8px;

  .event-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
    color: #212529;
    margin-bottom: 6px;
  }

  .event-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .event-tag {
      font-size: 11px;
      font-weight: 500;
    }
  }
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .price-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .original-price {
      font-size: 13px;
      color: #6c757d;
      text-decoration: line-through;
    }

    .current-price {
      font-size: 18px;
      font-weight: 700;
      color: #ff8142;
    }
  }

  .rating-info {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
  }
}

.meta-info {
  margin-bottom: 12px;

  .datetime-info,
  .location-info {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    color: #6c757d;
  }

  .organizer-info {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
    margin-top: 6px;

    .organizer-name {
      margin-right: 8px;
    }

    .participant-count {
      margin-left: auto;
      font-weight: 500;
      color: #ff8142;
    }
  }
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .favorite-btn {
    color: #6c757d;
  }

  .book-btn {
    text-transform: none;
    font-weight: 600;
    font-size: 12px;
    padding: 6px 16px;
    border-radius: 8px;
  }
}

// Mobile responsive
@media (max-width: 599px) {
  .event-header .event-title {
    font-size: 14px;
  }

  .price-section .price-info .current-price {
    font-size: 16px;
  }

  .meta-info {
    .datetime-info,
    .location-info,
    .organizer-info {
      font-size: 11px;
    }
  }
}
</style>
