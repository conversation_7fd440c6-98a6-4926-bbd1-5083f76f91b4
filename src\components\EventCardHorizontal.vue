<template>
  <q-card class="event-card-horizontal" @click="$emit('click')">
    <div class="row no-wrap items-center">
      <!-- Image Section -->
      <div class="col-auto">
        <slot name="image">
          <q-img
            :src="photoUrl || '/images/image_default.jpg'"
            :alt="title"
            class="event-image"
            loading="lazy"
          >
            <!-- Status Badge Slot -->
            <slot name="badge">
              <div v-if="badge" class="status-badge" :style="badgeStyles">
                {{ badge }}
              </div>
            </slot>
          </q-img>
        </slot>
      </div>
      
      <!-- Content Section -->
      <div class="col q-pa-sm">
        <!-- Header Section -->
        <slot name="header">
          <div class="row items-center justify-between no-wrap">
            <div class="event-title">{{ title }}</div>
            
            <slot name="actions">
              <q-btn
                v-if="showFavorite"
                round
                flat
                dense
                size="sm"
                class="favorite-btn"
                @click.stop="toggleFavorite"
              >
                <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="16px" color="grey-6" />
              </q-btn>
            </slot>
          </div>
        </slot>
        
        <!-- Price Section -->
        <slot name="price">
          <div class="price-row" v-if="showPrice">
            <div v-if="originalPrice" class="original-price">{{ formatPrice(originalPrice) }}</div>
            <div class="price">{{ formatPrice(price) }}</div>
            <div class="participants-info" v-if="currentParticipants !== undefined && maxParticipants !== undefined">
              {{ currentParticipants }}/{{ maxParticipants }}
            </div>
          </div>
        </slot>
        
        <!-- Time Section -->
        <slot name="time">
          <div class="time-row" v-if="date || startTime">
            <div class="date-display" v-if="date">
              <q-icon name="access_time" size="12px" color="grey-6" class="q-mr-xs" />
              <span>{{ formattedDate }}</span>
            </div>
            <div class="time-display" v-if="startTime">
              {{ formattedTime }}
            </div>
          </div>
        </slot>
        
        <!-- Location Section -->
        <slot name="location">
          <div class="location-row" v-if="location">
            <div class="location-display">
              <q-icon name="location_on" size="12px" color="grey-6" class="q-mr-xs" />
              <span>{{ location }}</span>
              <span class="distance-chip" v-if="distance">{{ distance }}</span>
            </div>
            
            <!-- Tags/Skills -->
            <div v-if="tags && tags.length" class="tags-wrapper">
              <q-chip
                v-for="(tag, index) in displayTags"
                :key="index"
                size="sm"
                dense
                class="tag-chip"
              >
                {{ tag }}
              </q-chip>
              <q-chip
                v-if="tags.length > (maxVisibleTags || 0)"
                size="sm"
                dense
                class="more-chip"
              >
                +{{ tags.length - (maxVisibleTags || 0) }}
              </q-chip>
            </div>
          </div>
        </slot>
        
        <!-- Additional Details Slot -->
        <slot name="details"></slot>
        
        <!-- Action Button Slot -->
        <slot name="action-button">
          <div v-if="showBookButton" class="action-button">
            <q-btn
              color="primary"
              class="book-btn"
              :label="bookBtnText || 'Đặt chỗ'"
              size="sm"
              @click.stop="$emit('book')"
            />
          </div>
        </slot>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface Props {
  // Basic info
  title: string;
  photoUrl?: string;
  
  // Badge
  badge?: string;
  badgeColor?: string;
  badgeTextColor?: string;
  
  // Price and participants
  originalPrice?: number;
  price?: number;
  currentParticipants?: number;
  maxParticipants?: number;
  
  // Date and time
  date?: string;
  startTime?: string;
  endTime?: string;
  
  // Location
  location?: string;
  distance?: string;
  
  // Tags/Skills
  tags?: string[];
  maxVisibleTags?: number | undefined;
  
  // UI Controls
  showPrice?: boolean;
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  photoUrl: '',
  badgeColor: '#f44336',
  badgeTextColor: 'white',
  tags: () => [],
  maxVisibleTags: 2,
  showPrice: true,
  showFavorite: true,
  showBookButton: true,
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

const isFavorite = ref(props.defaultFavorite);

const toggleFavorite = (event: Event) => {
  event.stopPropagation();
  isFavorite.value = !isFavorite.value;
  emit('favorite', isFavorite.value);
};

const formatPrice = (price?: number) => {
  if (!price && price !== 0) return '';
  
  if (price === 0) return 'Miễn phí';
  
  if (price >= 1000000) {
    return `${(price / 1000000).toFixed(1)}tr đ`;
  } else if (price >= 1000) {
    return `${Math.round(price / 1000)}K đ`;
  }
  return `${price}đ`;
};

const formattedDate = computed(() => {
  if (!props.date) return '';
  
  try {
    const date = new Date(props.date);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Ngày mai';
    } else {
      return date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
    }
  } catch {
    return props.date;
  }
});

const formattedTime = computed(() => {
  if (!props.startTime) return '';
  
  let timeStr = props.startTime;
  if (props.endTime) {
    timeStr += ` - ${props.endTime}`;
  }
  
  return timeStr;
});

const displayTags = computed(() => {
  return props.tags?.slice(0, props.maxVisibleTags) || [];
});

const badgeStyles = computed(() => {
  return {
    backgroundColor: props.badgeColor,
    color: props.badgeTextColor
  };
});
</script>

<style lang="scss" scoped>
.event-card-horizontal {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
  
  &:active {
    transform: scale(0.98);
  }
  
  .event-image {
    width: 90px;
    height: 90px;
    border-radius: 12px;
    margin: 12px;
    position: relative;
    
    .status-badge {
      position: absolute;
      top: 4px;
      left: 4px;
      font-size: 10px;
      font-weight: 500;
      padding: 2px 6px;
      border-radius: 4px;
      white-space: nowrap;
    }
  }
  
  .event-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    color: #212529;
    margin-bottom: 4px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    flex: 1;
  }
  
  .favorite-btn {
    margin-left: 8px;
  }
  
  .price-row {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    
    .original-price {
      font-size: 13px;
      color: #6c757d;
      text-decoration: line-through;
      margin-right: 6px;
    }
    
    .price {
      font-size: 14px;
      font-weight: 700;
      color: #ff8142;
      margin-right: auto;
    }
    
    .participants-info {
      font-size: 12px;
      color: #6c757d;
    }
  }
  
  .time-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 12px;
    color: #6c757d;
  }
  
  .location-row {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 6px;
    
    .location-display {
      font-size: 12px;
      color: #6c757d;
      display: flex;
      align-items: center;
      
      .distance-chip {
        margin-left: auto;
        font-size: 11px;
        background: #f1f5f9;
        padding: 2px 4px;
        border-radius: 4px;
      }
    }
    
    .tags-wrapper {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: hidden;
      gap: 4px;
      
      .tag-chip {
        height: 20px;
        font-size: 10px;
        color: #4b5563;
        background-color: #e5e7eb;
      }
      
      .more-chip {
        height: 20px;
        font-size: 10px;
        color: #6b7280;
        background-color: #f3f4f6;
      }
    }
  }
  
  .action-button {
    margin-top: 8px;
    
    .book-btn {
      text-transform: none;
      font-weight: 500;
      font-size: 12px;
      padding: 4px 12px;
    }
  }
}

// Mobile optimizations
@media (max-width: 599px) {
  .event-card-horizontal {
    border-radius: 12px;
    
    .event-image {
      width: 80px;
      height: 80px;
      margin: 10px;
    }
    
    .event-title {
      font-size: 13px;
    }
  }
}
</style>