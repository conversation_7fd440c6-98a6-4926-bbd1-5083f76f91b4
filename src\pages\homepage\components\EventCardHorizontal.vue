<template>
  <CardHorizontal 
    :image-url="photoUrl" 
    :image-alt="title"
    @click="$emit('click')"
  >
    <template #badges>
      <div v-if="badges?.length" class="badges-container">
        <div 
          v-for="(badge, index) in badges" 
          :key="index"
          class="status-badge" 
          :style="getBadgeStyles(badge)"
        >
          {{ badge.text }}
        </div>
      </div>
    </template>

    <template #header>
      <div class="row items-center justify-between no-wrap">
        <div class="event-title">{{ title }}</div>
        <q-btn
          v-if="showFavorite"
          round
          flat
          dense
          size="sm"
          @click.stop="handleFavoriteClick"
        >
          <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="16px" color="grey-6" />
        </q-btn>
      </div>
    </template>
    
    <template #price>
      <div class="price-row" v-if="formattedPriceDisplay.current || formattedPriceDisplay.original">
        <div v-if="formattedPriceDisplay.original" class="original-price">{{ formattedPriceDisplay.original }}</div>
        <div class="price">{{ formattedPriceDisplay.current }}</div>
        <div class="participants-info" v-if="participantsText">{{ participantsText }}</div>
      </div>
    </template>
    
    <template #meta>
      <div class="datetime-row" v-if="formattedDateTime">
        <q-icon name="access_time" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ formattedDateTime }}</span>
      </div>
      
      <div class="location-row" v-if="location">
        <q-icon name="location_on" size="12px" color="grey-6" class="q-mr-xs" />
        <span>{{ location }}</span>
        <span class="distance-chip" v-if="distance">{{ distance }}</span>
      </div>
      
      <div class="rating-row" v-if="rating">
        <q-icon name="star" color="amber" size="14px" class="q-mr-xs" />
        <span>{{ rating }}</span>
      </div>
    </template>
    
    <template #actions>
      <div v-if="showBookButton" class="action-button">
        <q-btn
          color="primary"
          :label="bookBtnText"
          size="sm"
          @click.stop="$emit('book')"
        />
      </div>
    </template>
  </CardHorizontal>
</template>

<script setup lang="ts">
import CardHorizontal from 'src/components/CardHorizontal.vue';
import { useCard } from 'src/composables/useCard';
import type { Badge } from 'src/components/BaseCard.vue';

interface Props {
  // Basic info
  title: string;
  photoUrl?: string;
  
  // Badges
  badges?: Badge[];
  
  // Price and participants
  originalPrice?: number;
  price?: number;
  currentParticipants?: number;
  maxParticipants?: number;
  
  // Date and time
  date?: string;
  startTime?: string;
  endTime?: string;
  
  // Location
  location?: string;
  distance?: string;
  
  // Rating
  rating?: number;
  
  // UI Controls
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  photoUrl: '/images/image_default.jpg',
  badges: () => [],
  showFavorite: true,
  showBookButton: true,
  bookBtnText: 'Đặt chỗ',
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

// Use shared card logic
const {
  isFavorite,
  toggleFavorite,
  getBadgeStyles,
  formattedDateTime,
  participantsText,
  formattedPriceDisplay
} = useCard(props);

const handleFavoriteClick = (event: Event) => {
  const newFavoriteState = toggleFavorite(event);
  emit('favorite', newFavoriteState);
};
</script>

<style lang="scss" scoped>
.badges-container {
  position: absolute;
  top: 4px;
  left: 4px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  max-width: calc(100% - 8px);
}

.status-badge {
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.event-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  color: #212529;
  margin-bottom: 4px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  flex: 1;
}

.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  
  .original-price {
    font-size: 13px;
    color: #6c757d;
    text-decoration: line-through;
    margin-right: 6px;
  }
  
  .price {
    font-size: 14px;
    font-weight: 700;
    color: #ff8142;
    margin-right: auto;
  }
  
  .participants-info {
    font-size: 12px;
    color: #6c757d;
  }
}

.datetime-row,
.location-row,
.rating-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #6c757d;
  
  .distance-chip {
    margin-left: auto;
    font-size: 11px;
    background: #f1f5f9;
    padding: 2px 4px;
    border-radius: 4px;
  }
}

.action-button {
  margin-top: 8px;
  
  .q-btn {
    text-transform: none;
    font-weight: 500;
    font-size: 12px;
    padding: 4px 12px;
  }
}
</style>
