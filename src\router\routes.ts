import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // Main app
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'home',
        component: () => import('pages/homepage/HomePage.vue'),
      },
      {
        path: 'venues',
        name: 'venues',
        component: () => import('pages/venues/VenuesPage.vue'),
      },
      {
        path: 'bookings',
        name: 'bookings',
        component: () => import('pages/my-booking/BookingsPage.vue'),
      },
      {
        path: 'profile',
        name: 'profile',
        component: () => import('pages/profile/ProfilePage.vue'),
      },
    ],
  },
  {
    path: '/venues',
    component: () => import('layouts/MainLayout.vue'),
    redirect: '/venues',
    children: [
      {
        path: '',
        name: 'venues',
        component: () => import('pages/venues/VenuesPage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },

  {
    path: '/events',
    component: () => import('layouts/MainLayout.vue'),
    redirect: '/events',
    children: [
      {
        path: '',
        name: 'events',
        component: () => import('pages/events/EventsPage.vue'),
        beforeEnter: (to, from, next) => {
          if (!to.query.tab) {
            next({
              path: to.path,
              query: { ...to.query, tab: 'discovery' },
            });
          } else {
            next();
          }
        },
      },
    ],
    meta: { requiresAuth: true },
  },
  {
    path: '/events',
    component: () => import('layouts/EventLayout.vue'),
    redirect: '/events/:id',
    children: [
      {
        path: ':id',
        name: 'event-detail',
        component: () => import('pages/event-detail/EventDetailPage.vue'),
      },
      {
        path: 'create',
        name: 'create-event',
        component: () => import('pages/create-event/EventCreatePage.vue'),
      },
    ],
    meta: { requiresAuth: true },
  },
  // Auth routes
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    redirect: '/auth/intro',
    children: [
      {
        path: 'intro',
        name: 'intro',
        component: () => import('pages/auth/OnboardingPage.vue'),
        meta: { guestOnly: true },
      },
      {
        path: 'login',
        name: 'login',
        component: () => import('pages/auth/LoginPage.vue'),
        meta: { guestOnly: true },
      },
      {
        path: 'signup',
        name: 'signup',
        component: () => import('pages/auth/SignUpPage.vue'),
        meta: { guestOnly: true },
      },
    ],
  },

  // Onboarding flow
  {
    path: '/onboarding',
    component: () => import('layouts/OnboardingLayout.vue'),
    // meta: { requiresAuth: true },
    redirect: '/onboarding/profile',
    children: [
      {
        path: 'profile',
        name: 'update-profile',
        component: () => import('pages/onboarding/UpdateProfilePage.vue'),
      },
      {
        path: 'location',
        name: 'location-permission',
        component: () => import('pages/onboarding/LocationPage.vue'),
      },
      {
        path: 'sports',
        name: 'choose-sports',
        component: () => import('pages/onboarding/SportsPage.vue'),
      },
    ],
  },

  // Error page - wrap trong AuthLayout
  {
    path: '/:catchAll(.*)*',
    component: () => import('layouts/AuthLayout.vue'),
    children: [
      {
        path: '',
        name: 'error-404',
        component: () => import('pages/ErrorNotFound.vue'),
      },
    ],
  },
];
export default routes;
