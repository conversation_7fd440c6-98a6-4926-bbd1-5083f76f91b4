<template>
  <div class="events-section">
    <!-- Section Header -->
    <div class="section-header">
      <div class="section-title">
        <q-icon name="flash_on" color="primary" size="20px" class="q-mr-xs" />
        <span class="title-text">Sự kiện xế về</span>
        <q-chip color="red" text-color="white" size="sm" class="q-ml-xs">
          Hot
        </q-chip>
      </div>
      <q-btn 
        flat 
        no-caps 
        color="primary" 
        class="see-all-btn"
        @click="$emit('seeAll')"
      >
        Xem tất cả
        <q-icon name="chevron_right" size="16px" class="q-ml-xs" />
      </q-btn>
    </div>

    <!-- Events Scroll Container -->
    <div class="events-scroll-container">
      <div class="events-list">
        <EventCardVertical
          v-for="event in events"
          :key="event.id"
          :event="event"
          class="event-card-item"
          @click="handleEventClick(event)"
          @book="handleBookEvent(event)"
          @favorite="handleFavoriteEvent(event, $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { computed } from 'vue';
import EventCardVertical from './EventCardVertical.vue';
import type { Event } from '../data/mockData';
import type { Badge } from 'src/components/BaseCard.vue';

interface Props {
  events: Event[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  seeAll: [];
  eventClick: [event: Event];
  bookEvent: [event: Event];
  favoriteEvent: [event: Event, isFavorite: boolean];
}>();

const getBadges = (event: Event): Badge[] => {
  if (!event.badge) return [];
  
  // Determine badge color based on content
  let color = '#f44336'; // default red
  if (event.badge.includes('ngày')) color = '#ff9800'; // orange for time-based
  if (event.badge.includes('Sắp')) color = '#4caf50'; // green for status
  
  return [{
    text: event.badge,
    color,
    textColor: 'white'
  }];
};

const handleEventClick = (event: Event) => {
  emit('eventClick', event);
};

const handleBookEvent = (event: Event) => {
  emit('bookEvent', event);
};

const handleFavoriteEvent = (event: Event, isFavorite: boolean) => {
  emit('favoriteEvent', event, isFavorite);
};
</script>

<style lang="scss" scoped>
.events-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px;
  
  .section-title {
    display: flex;
    align-items: center;
    
    .title-text {
      font-size: 18px;
      font-weight: 600;
      color: #212529;
    }
  }
  
  .see-all-btn {
    font-size: 14px;
    font-weight: 500;
    padding: 4px 8px;
  }
}

.events-scroll-container {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.events-list {
  display: flex;
  gap: 16px;
  padding: 0 16px;
  scroll-snap-type: x mandatory;
  
  .event-card-item {
    flex: 0 0 280px;
    scroll-snap-align: start;
  }
}

// Mobile optimizations
@media (max-width: 599px) {
  .section-header {
    padding: 0 12px 12px;
    
    .section-title .title-text {
      font-size: 16px;
    }
    
    .see-all-btn {
      font-size: 13px;
    }
  }
  
  .events-list {
    gap: 12px;
    padding: 0 12px;
    
    .event-card-item {
      flex: 0 0 260px;
    }
  }
}
</style>
