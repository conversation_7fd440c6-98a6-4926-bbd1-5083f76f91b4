import { api } from 'src/boot/axios';

import type {
  IActivity,
  IActivityQueryParams,
  IApiResponse,
  ICreateActivity,
  IPaginationResponse,
  ISkillLevel,
  ISport,
  ITag,
} from '../types/activities';

export class ActivitiesService {
  static async createActivity(
    data: ICreateActivity,
  ): Promise<IApiResponse<{ activity: IActivity }>> {
    const response = await api.post('/activities', data);
    return response.data;
  }

  static async getActivities(params: IActivityQueryParams): Promise<
    IApiResponse<{
      activities: IActivity[];
      pagination: IPaginationResponse;
    }>
  > {
    const response = await api.get('/activities', { params });
    return response.data;
  }

  static async getActivity(id: string): Promise<IApiResponse<{ activity: IActivity }>> {
    const response = await api.get(`/activities/${id}`);
    return response.data;
  }

  static async uploadActivityPhoto(
    id: string,
    file: File,
  ): Promise<IApiResponse<{ photoUrl: string }>> {
    const formData = new FormData();
    formData.append('photo', file);
    const response = await api.post(`/activities/${id}/photo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  static async getSports(): Promise<IApiResponse<{ sports: ISport[] }>> {
    const response = await api.get('/activities/sports');
    return response.data;
  }

  static async getSkillLevels(
    sportId: string,
  ): Promise<IApiResponse<{ skillLevels: ISkillLevel[] }>> {
    const response = await api.get(`/activities/sports/${sportId}/skill-levels`);
    return response.data;
  }

  static async getTags(): Promise<IApiResponse<{ tags: ITag[] }>> {
    const response = await api.get('/activities/tags');
    return response.data;
  }
}
