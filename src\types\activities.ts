export type ActivityEventType = 'TUYEN_GIAO_LUU' | 'XE_VE' | 'OTHER';
export type ActivityStatus = 'DRAFT' | 'PUBLISHED' | 'CANCELLED' | 'COMPLETED';

export interface IVenue {
  id: string;
  name: string;
  address: string;
  placeId: string | null;
  latitude: number | null;
  longitude: number | null;
  createdAt: string;
  updatedAt: string;
}

export interface ISport {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface ISkillLevel {
  id: string;
  name: string;
  sportId: string;
  createdAt: string;
  updatedAt: string;
}

export interface ITag {
  id: string;
  name: string;
  isCustom: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IUser {
  id: string;
  firebaseUid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  role: string;
}

export interface IActivity {
  id: string;
  title: string;
  description: string | null;
  eventType: ActivityEventType;
  status: ActivityStatus;
  date: string;
  startTime: string;
  endTime: string;
  maxPlayers: number;
  price: number;
  photoUrl: string | null;
  customLocation: string | null;
  createdAt: string;
  updatedAt: string;
  sport: ISport;
  venue: IVenue | null;
  creator: IUser;
  skillLevels: ISkillLevel[];
  tags: ITag[];
  participantCount: number;
  distance?: number;
}

export interface ICreateVenue {
  name: string;
  address: string;
  placeId?: string;
  latitude?: number;
  longitude?: number;
}

export interface ICreateActivity {
  title: string;
  description?: string;
  eventType?: ActivityEventType;
  status?: ActivityStatus;
  sportId: string;
  skillLevelIds: string[];
  date: string;
  startTime: string;
  endTime: string;
  venueId?: string;
  customLocation?: string;
  newVenue?: ICreateVenue;
  maxPlayers: number;
  price: number;
  tagIds?: string[];
  customTags?: string[];
}

export interface IActivityQueryParams {
  page?: number;
  limit?: number;
  sportId?: string;
  eventType?: ActivityEventType;
  status?: ActivityStatus;
  startDate?: string;
  endDate?: string;
  location?: string;
  skillLevel?: string;
  tags?: string;
  creatorId?: string;
  nearby?: boolean;
  lat?: number;
  lng?: number;
}

export interface IPaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface IApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}
