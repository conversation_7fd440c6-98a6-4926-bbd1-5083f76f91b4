<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card class="success-dialog" style="width: 90%; max-width: 400px">
      <q-card-section class="bg-primary text-white text-center">
        <div class="text-h6 text-weight-bold">Successfully Book</div>
      </q-card-section>

      <q-card-section class="q-py-md">
        <div class="warning-box q-pa-md q-mb-md">
          <div class="row items-start no-wrap">
            <TriangleAlert color="#ff8142" :size="40" class="q-mr-sm" />
            <div class="text-body2 text-orange-9">
              Please contact the host to confirm your attendance before proceeding with your
              booking.
            </div>
          </div>
        </div>

        <q-item clickable class="phone-item q-pa-md">
          <q-item-section avatar>
            <Phone color="#ff8142" :size="24" />
          </q-item-section>
          <q-item-section>
            <div class="text-body1 text-weight-medium">{{ props.phoneNumber }}</div>
          </q-item-section>
        </q-item>
      </q-card-section>

      <q-card-actions align="center" class="q-pa-md">
        <q-btn
          class="full-width"
          color="primary"
          size="lg"
          label="I've Contacted the Host"
          @click="$emit('confirm')"
        />
        <q-separator vertical inset class="q-mx-md" />
        <q-btn
          class="full-width"
          color="grey-3"
          text-color="grey-8"
          size="lg"
          label="Cancel"
          @click="$emit('cancel')"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { Phone, TriangleAlert } from 'lucide-vue-next';
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  phoneNumber: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
</script>

<style scoped>
.success-dialog {
  border-radius: 12px;
}

.warning-box {
  background-color: #fff3e0;
  border-radius: 8px;
  border: 1px solid #ff8142;
}

.phone-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.q-btn {
  border-radius: 8px;
  height: 44px;
}
</style>
