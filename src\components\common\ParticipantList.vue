<template>
  <q-item class="q-mt-sm rounded-borders q-pa-none">
    <q-item-section>
      <div class="row items-center" style="flex-wrap: nowrap">
        <div class="avatar-group">
          <q-avatar
            v-for="(participant, index) in props.participants.slice(0, props.maxParticipants)"
            :key="participant.id"
            :size="props.avatarSize"
            class="avatar-item"
            :style="`z-index: ${props.maxParticipants + index}; margin-left: ${index === 0 ? '0' : '-5px'}`"
          >
            <img :src="participant.avatar" alt="participant" />
          </q-avatar>
          <q-chip
            v-if="props.participants.length > props.maxParticipants && !props.avatarOnly"
            dense
            class="avatar-count"
          >
            +{{ props.participants.length - props.maxParticipants }}
          </q-chip>
        </div>
        <div v-if="!props.avatarOnly" class="q-ml-sm text-black text-weight-regular text-caption">
          {{ props.participants.length }}/{{ props.totalParticipants }} tham gia
        </div>
      </div>

      <div v-if="!props.avatarOnly" class="text-caption text-grey-8 q-mt-xs">
        {{ formatParticipantNames(props.participants) }}
      </div>
    </q-item-section>

    <q-item-section side v-if="props.showSideActions">
      <slot name="sideActions"></slot>
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
interface Participant {
  id: number;
  name: string;
  avatar: string;
  phone?: string;
}

interface ParticipantListProps {
  participants: Participant[];
  totalParticipants: number;
  maxParticipants: number;
  showSideActions: boolean;
  avatarOnly?: boolean;
  avatarSize?: string;
}

const props = withDefaults(defineProps<ParticipantListProps>(), {
  maxParticipants: 3,
  showSideActions: true,
  avatarOnly: false,
  avatarSize: '24px',
});

const formatParticipantNames = (participants: Participant[]): string => {
  const names = participants.map((p) => p.name);
  if (names.length <= 2) {
    return names.join(', ');
  }
  return `${names[0]} và ${participants.length - 2} người khác`;
};
</script>

<style scoped lang="scss">
.avatar-group {
  display: flex;
  align-items: center;

  .avatar-item {
    border: 2px solid white;

    img {
      object-fit: cover;
    }
  }

  .avatar-count {
    min-width: 24px;
    height: 24px;
    margin: 0 !important;
    margin-top: 4px !important;
    margin-left: -12px !important;
    z-index: 1000;
    border-radius: 100%;
  }
}
</style>
