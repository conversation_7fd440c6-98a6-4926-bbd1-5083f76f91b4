<template>
  <div 
    class="sport-category-card" 
    :class="{ 'active': active }"
    @click="$emit('click')"
  >
    <div class="sport-icon" :style="{ backgroundColor: iconBgColor }">
      <span class="icon-emoji">{{ sport.icon }}</span>
    </div>
    <div class="sport-name">{{ sport.name }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Sport {
  id: string;
  name: string;
  icon: string;
  color: string;
}

interface Props {
  sport: Sport;
  active?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  active: false
});

defineEmits<{
  click: [];
}>();

const iconBgColor = computed(() => {
  if (props.active) {
    return props.sport.color;
  }
  return `${props.sport.color}20`; // 20% opacity
});
</script>

<style lang="scss" scoped>
.sport-category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &.active {
    border-color: #ff8142;
    box-shadow: 0 4px 12px rgba(255, 129, 66, 0.2);
    
    .sport-icon {
      transform: scale(1.1);
    }
    
    .sport-name {
      color: #ff8142;
      font-weight: 600;
    }
  }
  
  .sport-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    .icon-emoji {
      font-size: 24px;
      line-height: 1;
    }
  }
  
  .sport-name {
    font-size: 12px;
    font-weight: 500;
    color: #212529;
    text-align: center;
    line-height: 1.2;
    transition: all 0.2s ease;
  }
}

@media (max-width: 600px) {
  .sport-category-card {
    padding: 10px 6px;
    gap: 6px;
    
    .sport-icon {
      width: 44px;
      height: 44px;
      border-radius: 10px;
      
      .icon-emoji {
        font-size: 22px;
      }
    }
    
    .sport-name {
      font-size: 11px;
    }
  }
}

@media (max-width: 480px) {
  .sport-category-card {
    padding: 8px 4px;
    gap: 4px;
    
    .sport-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      
      .icon-emoji {
        font-size: 20px;
      }
    }
    
    .sport-name {
      font-size: 10px;
    }
  }
}
</style>
