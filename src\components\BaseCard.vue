<template>
  <q-card class="base-card" @click="$emit('click')">
    <slot />
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

export interface Badge {
  text: string;
  color?: string;
  textColor?: string;
}

export interface BaseCardProps {
  // Core content
  title: string;
  subtitle?: string;
  photoUrl?: string;
  
  // Badges
  badges?: Badge[];
  
  // Pricing
  originalPrice?: number;
  price?: number;
  
  // DateTime
  date?: string;
  startTime?: string;
  endTime?: string;
  
  // Location
  location?: string;
  distance?: string;
  
  // Participants & Rating
  currentParticipants?: number;
  maxParticipants?: number;
  rating?: number;
  
  // UI Controls
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<BaseCardProps>(), {
  photoUrl: '/images/image_default.jpg',
  badges: () => [],
  showFavorite: true,
  showBookButton: true,
  bookBtnText: 'Đặt ngay',
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

// Shared reactive state
const isFavorite = ref(props.defaultFavorite);

// Shared methods
const toggleFavorite = (event: Event) => {
  event.stopPropagation();
  isFavorite.value = !isFavorite.value;
  emit('favorite', isFavorite.value);
};

const formatPrice = (price?: number) => {
  if (!price && price !== 0) return '';
  if (price === 0) return 'Miễn phí';
  if (price >= 1000000) return `${(price / 1000000).toFixed(1)}tr đ`;
  if (price >= 1000) return `${Math.round(price / 1000)}K đ`;
  return `${price}đ`;
};

const getBadgeStyles = (badge: Badge) => ({
  backgroundColor: badge.color || '#f44336',
  color: badge.textColor || 'white'
});

// Computed properties
const formattedDate = computed(() => {
  if (!props.date) return '';
  
  try {
    const date = new Date(props.date);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) return 'Hôm nay';
    if (date.toDateString() === tomorrow.toDateString()) return 'Ngày mai';
    return date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
  } catch {
    return props.date;
  }
});

const formattedTime = computed(() => {
  if (!props.startTime) return '';
  return props.endTime ? `${props.startTime} - ${props.endTime}` : props.startTime;
});

const formattedDateTime = computed(() => {
  const parts = [formattedDate.value, formattedTime.value].filter(Boolean);
  return parts.join(' ');
});

const participantsText = computed(() => {
  if (props.currentParticipants === undefined || props.maxParticipants === undefined) return '';
  return `${props.currentParticipants}/${props.maxParticipants}`;
});

// Expose shared functionality
defineExpose({
  isFavorite,
  toggleFavorite,
  formatPrice,
  getBadgeStyles,
  formattedDate,
  formattedTime,
  formattedDateTime,
  participantsText
});
</script>

<style lang="scss" scoped>
.base-card {
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
  cursor: pointer;
  
  &:active {
    transform: scale(0.98);
  }
}

@media (max-width: 599px) {
  .base-card {
    border-radius: 12px;
  }
}
</style>
