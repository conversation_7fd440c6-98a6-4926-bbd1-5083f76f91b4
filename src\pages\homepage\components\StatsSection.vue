<template>
  <div class="stats-cards-container">
    <div class="stats-cards-scroll">
      <q-card v-for="(stat, index) in stats" :key="index" class="stats-card">
        <q-card-section class="text-center">
          <component :is="stat.icon" :size="24" :color="stat.color" class="q-mb-sm" />
          <div class="text-h6 text-weight-bold">{{ stat.value }}</div>
          <div class="text-caption">{{ stat.label }}</div>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CalendarCheck, Users, MapPin, Star } from 'lucide-vue-next';

const stats = [
  { 
    icon: CalendarCheck, 
    value: '1,234', 
    label: 'Sự kiện',
    color: '#3b82f6'
  },
  { 
    icon: Users, 
    value: '45K', 
    label: 'Thành viên',
    color: '#10b981'
  },
  { 
    icon: MapPin, 
    value: '567', 
    label: 'Sân thể thao',
    color: '#8b5cf6' 
  },
  { 
    icon: Star, 
    value: '4.9★', 
    label: 'Đánh giá',
    color: '#f59e0b'
  }
];
</script>

<style lang="scss" scoped>
.stats-cards-container {
  width: 100%;
  .stats-cards-scroll {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
    gap: 12px;

    // Hide scrollbar
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;

    .stats-card {
      flex: 0 0 auto;
      width: calc(40% - 8px);
      scroll-snap-align: start;
      transition:
        transform 0.2s ease,
        box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .text-h6 {
        font-size: 22px;
        margin: 4px 0;
        color: #212529;
      }
      
      .text-caption {
        color: #6c757d;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

// Media queries for responsive design
@media (min-width: 600px) {
  .stats-cards-container {
    .stats-cards-scroll {
      .stats-card {
        width: calc(25% - 12px);
      }
    }
  }
}

@media (min-width: 1024px) {
  .stats-cards-container {
    .stats-cards-scroll {
      justify-content: space-between;
      flex-wrap: nowrap;

      .stats-card {
        flex: 1;
        width: calc(25% - 16px);
      }
    }
  }
}
</style> 