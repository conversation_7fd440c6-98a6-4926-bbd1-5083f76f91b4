<template>
  <div class="empty-data">
    <q-icon name="image_search" size="64px" color="grey-5" />
    <div class="text-h6 q-mt-md text-grey-6">{{ title ?? '<PERSON>h<PERSON>ng tìm thấy sự kiện nào' }}</div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title?: string;
  icon?: string;
}>();
</script>

<style scoped>
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>