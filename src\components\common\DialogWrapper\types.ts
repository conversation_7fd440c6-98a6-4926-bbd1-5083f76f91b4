// DialogWrapper component types

export interface DialogWrapperProps {
  /** Dialog title displayed in header */
  title?: string;
  
  /** Whether to show the header section */
  showHeader?: boolean;
  
  /** Whether to show the footer section */
  showFooter?: boolean;
  
  /** Whether to show the close button in header */
  showCloseButton?: boolean;
  
  /** Maximum width of the dialog */
  maxWidth?: string;

  /** Minimum width of the dialog */
  minWidth?: string;
  
  /** Maximum height of the dialog */
  maxHeight?: string;
  
  /** Whether dialog should take full height on mobile */
  fullHeight?: boolean;
}

export interface DialogWrapperEmits {
  /** Emitted when close button is clicked */
  (e: 'close'): void;
}

export interface DialogWrapperSlots {
  /** Content slot for main dialog content */
  content(): any;

  /** Footer slot for action buttons */
  footer(): any;
}

// Common dialog configurations
export const DialogConfigs = {
  /** Small dialog configuration */
  small: {
    maxWidth: '400px',
    maxHeight: '70vh'
  },
  
  /** Medium dialog configuration (default) */
  medium: {
    maxWidth: '500px',
    maxHeight: '80vh'
  },
  
  /** Large dialog configuration */
  large: {
    maxWidth: '700px',
    maxHeight: '90vh'
  },
  
  /** Full screen mobile configuration */
  fullscreen: {
    maxWidth: '100%',
    maxHeight: '100vh',
    fullHeight: true
  }
} as const;

export type DialogSize = keyof typeof DialogConfigs;
