<template>
  <!-- Activity Slide -->
  <div class="column justify-center">
    <div class="row items-center justify-between">
      <div class="text-h5 text-bold text-black-4">Sự kiện sắp tới</div>
      <router-link to="/events" class="text-primary"> Xem tất cả </router-link>
    </div>
    <div v-if="eventItems.length === 0" class="text-center q-pa-md text-grey-6">
      Không có sự kiện nào
    </div>
    <BaseSlider v-else :itemsSlide="eventItems">
      <template #default="{ item }">
        <EventCard :item="item">
          <template #footerActions>
            <div v-if="item.price" class="text-primary text-bold">
              ${{ item.price.toFixed(2) }}
              <span class="text-grey-8 text-body1 text-weight-regular">/người </span>
            </div>
            <q-btn
              label="Tham gia"
              outline
              color="primary"
              style="min-height: 24px"
              rounded
              @click.stop="handleJoinEvent(item)"
            />
          </template>
        </EventCard>
      </template>
    </BaseSlider>
  </div>
</template>

<script setup lang="ts">
import EventCard from 'src/components/EventCard.vue';
import BaseSlider from 'src/components/common/BaseSlider.vue';
import type { EventJoinData, useEventJoinFlow } from 'src/composables/events/useEventJoinFlow';
import { inject } from 'vue';

// Inject the global event join flow instance
const eventJoinFlow = inject<ReturnType<typeof useEventJoinFlow>>('eventJoinFlow');

const handleJoinEvent = (item) => {
  if (!eventJoinFlow) return;

  const eventData: EventJoinData = {
    id: item.id,
    title: item.title,
    price: item.price,
    date: item.date,
    startTime: item.startTime,
    endTime: item.endTime,
    photoUrl: item.photoUrl,
    organizer: {
      name: 'Host',
      phoneNumber: item.hostPhoneNumber || '+1 (212) 2222222',
    },
  };

  eventJoinFlow.joinEvent(eventData);
};

const eventItems = [
  {
    id: '1',
    photoUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
    description: 'Giải đấu bóng đá dành cho các đội nghiệp dư tại Hà Nội',
    skillLevels: ['Intermediate'],
    title: 'Acoustic Serenade Showcase',
    location: 'New York, USA',
    startTime: '10:00PM',
    endTime: '12:00 PM',
    date: 'May 29 ',
    price: 30,
  },
  {
    id: '2',
    photoUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
    skillLevels: ['Intermediate'],
    description: 'Giải đấu bóng đá dành cho các đội nghiệp dư tại Hà Nội',
    title: 'Acoustic Serenade Showcase',
    location: 'New York, USA',
    startTime: '10:00PM',
    endTime: '12:00 PM',
    date: 'May 29 ',
    price: 30,
    buttonText: 'Tham gia',
  },
  {
    id: '3',
    photoUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
    skillLevels: ['Intermediate'],
    description: 'Giải đấu bóng đá dành cho các đội nghiệp dư tại Hà Nội',
    title: 'Acoustic Serenade Showcase',
    location: 'New York, USA',
    startTime: '10:00PM',
    endTime: '12:00 PM',
    date: 'May 29 ',
    price: 30,
    buttonText: 'Tham gia',
  },
  {
    id: '4',
    photoUrl: 'https://cdn.quasar.dev/img/parallax1.jpg',
    skillLevels: ['Intermediate'],
    description: 'Giải đấu bóng đá dành cho các đội nghiệp dư tại Hà Nội',
    title: 'Acoustic Serenade Showcase',
    location: 'New York, USA',
    startTime: '10:00PM',
    endTime: '12:00 PM',
    date: 'May 29 ',
    price: 30,
    buttonText: 'Tham gia',
  },
];
</script>

<style scoped lang="scss">
.card-event {
  position: relative;

  .q-img {
    border-top-left-radius: 12px !important;
    border-top-right-radius: 12px !important;
  }

  .upload-icon {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 100%;
    width: 2rem;
    height: 2rem;
    border: none;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
}
</style>
