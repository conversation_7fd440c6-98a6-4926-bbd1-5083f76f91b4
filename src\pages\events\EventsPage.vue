<template>
  <q-page class="bg-grey-1 events-page">
    <!-- Loading Skeleton -->
    <PageSkeleton v-if="isLoading" />

    <!-- Actual Content -->
    <div v-else>
      <!-- Search Section -->
      <section class="bg-white q-px-md q-py-md">
        <div class="row justify-center">
          <div class="col-12 col-md-12 col-lg-12">
            <q-input
              v-model="searchQuery"
              outlined
              :placeholder="t('events.searchPlaceholder', 'Tìm kiếm sự kiện...')"
              class="search-input"
              bg-color="white"
              clearable
            >
              <template v-slot:prepend>
                <q-icon color="grey-6" name="search" />
              </template>
              <template v-slot:append>
                <q-btn
                  flat
                  round
                  dense
                  @click="openFilterDialog"
                  color="primary"
                  icon="tune"
                />
              </template>
            </q-input>
          </div>
        </div>
      </section>

       <!-- Main Content Tabs -->
      <section class="content-tabs q-px-md">
        <div class="tabs-container">
          <div class="tabs-container-left">
            <q-tabs
              v-model="currentTab"
              class="main-tabs"
              indicator-color="primary"
              active-color="primary"
              align="left"
              no-caps
              dense
              inline-label
            >
              <q-tab name="discovery" label="Khám phá" />
              <q-tab name="my-events" label="Sự kiện của tôi" />
            </q-tabs>
          </div>
          <div class="tabs-container-right">
            <!-- Compact View Mode Toggle -->
            <div class="view-mode-toggle">
              <q-btn
                flat
                round
                size="sm"
                :color="viewMode === 'card' ? 'primary' : 'grey-6'"
                @click="viewMode = 'card'"
                class="toggle-btn"
              >
                <LayoutGrid :size="16" />
              </q-btn>
              <q-btn
                flat
                round
                size="sm"
                :color="viewMode === 'list' ? 'primary' : 'grey-6'"
                @click="viewMode = 'list'"
                class="toggle-btn"
              >
                <LayoutList :size="16" />
              </q-btn>
            </div>
          </div>
        </div>
      </section>
      
      <!-- Tab Panels -->
      <section class="tab-content">
        <q-tab-panels v-model="currentTab" animated class="bg-transparent" no-caps dense>
          <!-- Discovery Tab -->
          <q-tab-panel name="discovery" class="q-pa-none">
            <div class="events-content q-px-md q-mt-sm">
              <TabsSkeleton v-if="isTabLoading" :isTabLoading="isTabLoading" />
              <EventDiscovery
                v-else
                :searchQuery="searchQuery"
                :formatDate="formatDate"
                :formatPrice="formatPrice"
                :closeDialogs="fabOpen"
                :viewMode="viewMode"
                @dialogStateChanged="onDialogStateChanged"
              />
            </div>
          </q-tab-panel>

          <!-- My Events Tab -->
          <q-tab-panel name="my-events" class="q-pa-none">
            <div class="events-content q-px-md q-mt-sm">
              <TabsSkeleton v-if="isTabLoading" :isTabLoading="isTabLoading" />
              <MyEvent
                v-else
                :searchQuery="searchQuery"
                :formatDate="formatDate"
                :formatPrice="formatPrice"
              />
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </section>

      <!-- Filter Dialog -->
      <q-dialog v-model="showFilterDialog" position="bottom">
        <FilterDialog
          @apply="applyFilters"
          @close="showFilterDialog = false"
        />
      </q-dialog>

      <!-- Create Event Dialog From Template -->
      <q-dialog v-model="createEventFromTemplateDialog" position="bottom">
        <EventTemplateDialog />
      </q-dialog>
    </div>

    <!-- Overlay when FAB is open -->
    <div v-if="fabOpen" class="fixed-full bg-black" style="opacity: 0.6; z-index: 5000"></div>

    <!-- Floating Action Button -->
    <q-page-sticky position="bottom-right" :offset="[18, 28]" style="z-index: 5001;">
      <q-fab
        v-model="fabOpen"
        color="primary"
        vertical-actions-align="right"
        icon="add"
        direction="up"
        padding="sm"
        size="lg"
        @show="onFabShow"
      >
        <template v-slot:icon="{ opened }">
          <q-icon :class="{ 'example-fab-animate--hover': opened !== true }" name="add" />
        </template>
        <q-fab-action
          color="blue"
          icon="filter_list"
          label="Từ mẫu sự kiện"
          @click="createEventFromTemplate"
          label-position="right"
        />
        <q-fab-action
          color="green"
          icon="event"
          label="Sự kiện mới"
          @click="createEvent"
          label-position="right"
        />
      </q-fab>
    </q-page-sticky>
  </q-page>
</template>

<script setup lang="ts">
import EventDiscovery from 'src/pages/events/components/EventDiscovery.vue';
import MyEvent from 'src/pages/events/components/MyEvent.vue';
import EventTemplateDialog from 'src/pages/events/components/dialogs/EventTemplateDialog.vue';
import FilterDialog from 'src/components/FilterDialog.vue';
import PageSkeleton from 'src/pages/events/components/skeleton/PageSkeleton.vue';
import { onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { LayoutList, LayoutGrid } from 'lucide-vue-next';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const currentTab = ref('discovery');
const searchQuery = ref('');
const isLoading = ref(true);
const isTabLoading = ref(false);
const fabOpen = ref(false);
const hasOpenDialogs = ref(false);
const createEventFromTemplateDialog = ref(false);
const showFilterDialog = ref(false);

// View mode state with localStorage persistence
const viewMode = ref(localStorage.getItem('eventDiscoveryViewMode') ?? 'card');

// Watch view mode changes and persist to localStorage
watch(viewMode, (newMode) => {
  localStorage.setItem('eventDiscoveryViewMode', newMode);
});

const onFabShow = () => {
  if (hasOpenDialogs.value) {
    fabOpen.value = false;
  }
};

const onDialogStateChanged = (isOpen) => {
  hasOpenDialogs.value = isOpen;
};

onMounted(async () => {
  currentTab.value = (route.query.tab as string) || 'discovery';
  searchQuery.value = (route.query.search as string) || '';
  await new Promise((resolve) => setTimeout(resolve, 1500));
  isLoading.value = false;
});

// Watch for tab changes to trigger loading state
watch(currentTab, async (newTab) => {
  await router.replace({
    query: {
      ...route.query,
      tab: newTab,
    },
  });

  isTabLoading.value = true;
  // Simulate loading delay
  await new Promise((resolve) => setTimeout(resolve, 1000));
  isTabLoading.value = false;
});

watch(searchQuery, (newQuery) => {
  const timeoutId = setTimeout(() => {
    router.replace({
      query: {
        ...route.query,
        search: newQuery || undefined,
      },
    });
  }, 500);

  return () => clearTimeout(timeoutId);
});

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(price);
};

const createEvent = () => {
  fabOpen.value = false;
  router.push('/events/create');
};

const createEventFromTemplate = () => {
  createEventFromTemplateDialog.value = true;
};

const openFilterDialog = () => {
  showFilterDialog.value = true;
};

const applyFilters = (filters: any) => {
  // Handle filter application logic here
  console.log('Applied filters:', filters);
  showFilterDialog.value = false;
};
</script>

<style scoped lang="scss">
// Content Tabs
.content-tabs {
  background: white;
  border-bottom: 1px solid #e9ecef;

  .tabs-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
  }

  .main-tabs {
    flex: 1;

    :deep(.q-tab) {
      font-size: 15px;
      font-weight: 500;
      color: #6c757d;
      text-transform: none;
      min-height: 48px;
      transition: all 0.3s ease;

      &.q-tab--active {
        color: var(--q-primary);
        font-weight: 600;
        border-bottom-color: var(--q-primary);
      }
    }

    :deep(.q-tab-panels) {
      background: transparent;
    }
  }
}

/* Search Input Styling */
.search-input :deep(.q-field__control) {
  border-radius: 24px;
  box-shadow: 0 0px 8px rgba(0, 0, 0, 0.12);
  height: 52px;
}

.search-input :deep(.q-field__control):before,
.search-input :deep(.q-field__control):after {
  border: none;
}

/* Tab Content */
.tab-content {
  background: white;
  min-height: calc(100vh - 200px);
}

/* Events Content */
.events-content {
  background: white;
}

/* Mobile Responsive */
@media (max-width: 599px) {
  .content-tabs {
    .tabs-container {
      gap: 12px;
    }
  }
}

// Compact View Mode Toggle
.view-mode-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.toggle-btn {
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 129, 66, 0.1) !important;
  }

  // Active state with background like hover
  &.text-primary {
    background: rgba(255, 129, 66, 0.12) !important;

    &:hover {
      background: rgba(255, 129, 66, 0.16) !important;
    }
  }
}

// Mobile Responsive Enhancements
@media (max-width: 600px) {
  .view-mode-toggle {
    gap: 2px;
  }

  .toggle-btn {
    &.text-primary {
      background: rgba(255, 129, 66, 0.1) !important;

      &:hover {
        background: rgba(255, 129, 66, 0.14) !important;
      }
    }
  }
}
</style>
