<template>
  <q-btn-dropdown
    ref="languageDropdown"
    flat
    :label="currentLanguageLabel"
    icon="language"
    dropdown-icon="keyboard_arrow_down"
  >
    <q-list>
      <q-item
        v-for="language in languages"
        :key="language.value"
        clickable
        @click="switchLanguage(language.value)"
        :class="{ 'bg-primary text-white cursor-pointer': currentLocale === language.value }"
      >
        <q-item-section avatar>
          <span class="text-h6">{{ language.flag }}</span>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ language.label }}</q-item-label>
          <q-item-label caption>{{ language.nativeLabel }}</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon v-if="currentLocale === language.value" name="check" color="positive" />
        </q-item-section>
      </q-item>
    </q-list>
  </q-btn-dropdown>
</template>

<script setup lang="ts">
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

interface Language {
  value: string;
  label: string;
  nativeLabel: string;
  flag: string;
}

const { locale } = useI18n();

const languageDropdown = ref();

const languages: Language[] = [
  {
    value: 'en-US',
    label: 'English',
    nativeLabel: 'English',
    flag: '🇺🇸',
  },
  {
    value: 'vi-VN',
    label: 'Vietnamese',
    nativeLabel: 'Tiếng Việt',
    flag: '🇻🇳',
  },
];

const currentLocale = computed(() => locale.value);

const currentLanguageLabel = computed(() => {
  const current = languages.find((lang) => lang.value === currentLocale.value);
  return current ? current.nativeLabel : 'Language';
});

// Load saved language on component mount
onMounted(() => {
  try {
    const savedLanguage = localStorage.getItem(LOCAL_STORAGE_KEYS.USER_LANGUAGE);
    if (savedLanguage && languages.some((lang) => lang.value === savedLanguage)) {
      locale.value = savedLanguage;
    }
  } catch (error) {
    console.error('Error loading saved language:', error);
  }
});

const switchLanguage = (lang: string) => {
  locale.value = lang;
  // Optionally save to localStorage for persistence
  localStorage.setItem(LOCAL_STORAGE_KEYS.USER_LANGUAGE, lang);
  // Close the dropdown menu
  languageDropdown.value?.hide();
};
</script>

<style scoped>
.q-item.bg-primary {
  border-radius: 4px;
}
</style>
