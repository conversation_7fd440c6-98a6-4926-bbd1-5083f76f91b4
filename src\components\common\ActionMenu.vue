<template>
  <q-btn
    :round="!fullWidth"
    flat
    dense
    :size="size"
    :class="[buttonClass, { 'full-width': fullWidth }]"
  >
    <q-icon :name="icon" :color="iconColor" :size="iconSize" />
    <q-menu :anchor="anchor" :self="self">
      <q-list dense :style="{ minWidth: menuWidth }">
        <!-- Share Option -->
        <q-item 
          v-if="showShare"
          clickable 
          v-close-popup 
          @click.stop="handleShare"
        >
          <q-item-section avatar>
            <q-icon name="share" color="primary" size="18px" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-body2">{{ shareLabel }}</q-item-label>
          </q-item-section>
        </q-item>
        
        <!-- Report Option -->
        <q-item 
          v-if="showReport"
          clickable 
          v-close-popup 
          @click.stop="handleReport"
        >
          <q-item-section avatar>
            <q-icon name="flag" color="red-5" size="18px" />
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-body2">{{ reportLabel }}</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Custom Actions Slot -->
        <slot name="actions" />
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
// Types
type MenuAnchor = 'bottom right' | 'top right' | 'top left' | 'top middle' | 'top start' | 'top end' | 'center left' | 'center middle' | 'center right' | 'center start' | 'center end' | 'bottom left' | 'bottom middle' | 'bottom start' | 'bottom end';

interface Props {
  // Button props
  size?: string;
  buttonClass?: string;
  icon?: string;
  iconColor?: string;
  iconSize?: string;
  fullWidth?: boolean;

  // Menu props
  anchor?: MenuAnchor;
  self?: MenuAnchor;
  menuWidth?: string;

  // Action visibility
  showShare?: boolean;
  showReport?: boolean;

  // Labels
  shareLabel?: string;
  reportLabel?: string;

  // Data for actions
  itemId?: string | number | undefined;
  itemType?: string;
}

const props = withDefaults(defineProps<Props>(), {
  // Button defaults
  size: 'sm',
  buttonClass: 'menu-btn',
  icon: 'more_vert',
  iconColor: 'white',
  iconSize: '16px',
  fullWidth: false,

  // Menu defaults
  anchor: 'bottom right',
  self: 'top right',
  menuWidth: '150px',

  // Action defaults
  showShare: true,
  showReport: true,

  // Label defaults
  shareLabel: 'Chia sẻ',
  reportLabel: 'Báo cáo',

  // Data defaults
  itemType: 'event'
});

const emit = defineEmits<{
  share: [itemId?: string | number];
  report: [itemId?: string | number];
}>();

// Methods
const handleShare = () => {
  emit('share', props.itemId);
};

const handleReport = () => {
  emit('report', props.itemId);
};
</script>

<style lang="scss" scoped>
.menu-btn {
  background-color: rgba(0, 0, 0, 0.7) !important;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
}

// Variants for different contexts
.menu-btn--light {
  background-color: rgba(255, 255, 255, 0.9) !important;
  
  &:hover {
    background-color: rgba(255, 255, 255, 1) !important;
  }
}

.menu-btn--transparent {
  background-color: transparent !important;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1) !important;
  }
}

// Full width variant
.full-width {
  width: 100% !important;
  border-radius: 8px !important;
  justify-content: flex-start !important;

  .q-btn__content {
    justify-content: flex-start !important;
    gap: 12px;
  }
}
</style>
