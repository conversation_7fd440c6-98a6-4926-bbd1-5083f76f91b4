<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card class="success-dialog" style="width: 90%; max-width: 400px">
      <q-card-section class="q-pa-md column items-center justify-center">
        <div class="check-icon q-mb-md">
          <CircleCheckBig color="#22c55e" :size="28" />
        </div>
        <div class="text-h6 q-mb-md text-center text-weight-bold">Booking Confirmed!</div>
        <div class="text-body2 text-grey-8 text-center">
          Thank you for contacting the host. You can now proceed with your booking.
        </div>
      </q-card-section>

      <q-card-actions align="center" class="q-px-md q-pb-md">
        <q-btn
          size="lg"
          color="primary"
          label="Tiếp tục"
          class="full-width"
          @click="$emit('confirm')"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { CircleCheckBig } from 'lucide-vue-next';
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(['update:modelValue', 'confirm']);

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
</script>

<style scoped>
.success-dialog {
  border-radius: 12px;
}

.check-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: #dcfce7;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
