<!-- <template>
  <CardOverlay 
    :image-url="photoUrl" 
    :image-alt="title"
    @click="$emit('click')"
  >
    <template #badges>
      <div v-if="badges?.length" class="badges-container">
        <div 
          v-for="(badge, index) in badges" 
          :key="index"
          class="status-badge" 
          :style="getBadgeStyles(badge)"
        >
          {{ badge.text }}
        </div>
      </div>
    </template>

    <template #favorite>
      <q-btn
        v-if="showFavorite"
        round
        flat
        dense
        size="sm"
        class="favorite-btn"
        @click.stop="handleFavoriteClick"
      >
        <q-icon :name="isFavorite ? 'favorite' : 'favorite_border'" size="16px" color="white" />
      </q-btn>
    </template>

    <template #overlay-header>
      <div class="overlay-title">{{ title }}</div>
      <div v-if="subtitle" class="overlay-subtitle">{{ subtitle }}</div>
    </template>
    
    <template #overlay-meta>
      <div class="overlay-meta">
        <div v-if="formattedDateTime" class="overlay-meta-item">
          <q-icon name="schedule" size="14px" class="q-mr-xs" />
          {{ formattedDateTime }}
        </div>
        <div v-if="location" class="overlay-meta-item">
          <q-icon name="place" size="14px" class="q-mr-xs" />
          {{ location }}
        </div>
      </div>
    </template>
    
    <template #overlay-price>
      <div class="overlay-price" v-if="formattedPriceDisplay.current || formattedPriceDisplay.original">
        <span v-if="formattedPriceDisplay.original" class="original-price">{{ formattedPriceDisplay.original }}</span>
        <span class="current-price">{{ formattedPriceDisplay.current }}</span>
      </div>
    </template>

    <template #bottom-content>
      <div class="row items-center justify-between q-pa-sm">
        <div class="row items-center">
          <div v-if="rating" class="rating-section">
            <q-icon name="star" color="amber" size="16px" class="q-mr-xs" />
            <span class="text-body2 text-weight-medium">{{ rating }}</span>
          </div>
          <div v-if="participantsText" class="participants-section q-ml-sm">
            <q-icon name="group" size="16px" class="q-mr-xs" />
            <span class="text-body2">{{ participantsText }}</span>
          </div>
        </div>
        <q-btn
          v-if="showBookButton"
          :label="bookBtnText"
          color="primary"
          size="sm"
          @click.stop="$emit('book')"
        />
      </div>
    </template>
  </CardOverlay>
</template>

<script setup lang="ts">
import CardOverlay from 'src/components/CardOverlay.vue';
import { useCard } from 'src/composables/useCard';
import type { Badge } from 'src/components/BaseCard.vue';

interface Props {
  // Basic info
  title: string;
  subtitle?: string;
  photoUrl?: string;
  
  // Badges
  badges?: Badge[];
  
  // Price and participants
  originalPrice?: number;
  price?: number;
  currentParticipants?: number;
  maxParticipants?: number;
  
  // Date and time
  date?: string;
  startTime?: string;
  endTime?: string;
  
  // Location
  location?: string;
  distance?: string;
  
  // Rating
  rating?: number;
  
  // UI Controls
  showFavorite?: boolean;
  showBookButton?: boolean;
  bookBtnText?: string;
  defaultFavorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  photoUrl: '/images/image_default.jpg',
  badges: () => [],
  showFavorite: true,
  showBookButton: true,
  bookBtnText: 'Đặt ngay',
  defaultFavorite: false
});

const emit = defineEmits<{
  click: [];
  book: [];
  favorite: [isFavorite: boolean];
}>();

// Use shared card logic
const {
  isFavorite,
  toggleFavorite,
  getBadgeStyles,
  formattedDateTime,
  participantsText,
  formattedPriceDisplay
} = useCard(props);

const handleFavoriteClick = (event: Event) => {
  const newFavoriteState = toggleFavorite(event);
  emit('favorite', newFavoriteState);
};
</script>

<style lang="scss" scoped>
.badges-container {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  .status-badge {
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    white-space: nowrap;
  }
}

.favorite-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
}

.overlay-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.overlay-subtitle {
  font-size: 14px;
  color: white;
  margin-bottom: 8px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.overlay-meta {
  margin-bottom: 8px;
  
  .overlay-meta-item {
    font-size: 12px;
    color: white;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

.overlay-price {
  .original-price {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: line-through;
    margin-right: 8px;
  }
  
  .current-price {
    font-size: 18px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

.rating-section,
.participants-section {
  display: flex;
  align-items: center;
  color: #6c757d;
}

@media (max-width: 599px) {
  .overlay-title {
    font-size: 16px;
  }
  
  .overlay-subtitle {
    font-size: 13px;
  }
  
  .overlay-price .current-price {
    font-size: 16px;
  }
}
</style> -->
