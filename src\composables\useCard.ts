import { computed, ref } from 'vue';
import type { Badge } from 'src/components/BaseCard.vue';

export interface UseCardProps {
  // Pricing
  originalPrice?: number;
  price?: number;
  
  // DateTime
  date?: string;
  startTime?: string;
  endTime?: string;
  
  // Participants & Rating
  currentParticipants?: number;
  maxParticipants?: number;
  rating?: number;
  
  // UI Controls
  defaultFavorite?: boolean;
}

export const useCard = (props: UseCardProps) => {
  // Reactive state
  const isFavorite = ref(props.defaultFavorite || false);

  // Methods
  const toggleFavorite = (event: Event) => {
    event.stopPropagation();
    isFavorite.value = !isFavorite.value;
    return isFavorite.value;
  };

  const formatPrice = (price?: number) => {
    if (!price && price !== 0) return '';
    if (price === 0) return 'Miễn phí';
    if (price >= 1000000) return `${(price / 1000000).toFixed(1)}tr đ`;
    if (price >= 1000) return `${Math.round(price / 1000)}K đ`;
    return `${price}đ`;
  };

  const getBadgeStyles = (badge: Badge) => ({
    backgroundColor: badge.color || '#f44336',
    color: badge.textColor || 'white'
  });

  // Computed properties
  const formattedDate = computed(() => {
    if (!props.date) return '';
    
    try {
      const date = new Date(props.date);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      if (date.toDateString() === today.toDateString()) return 'Hôm nay';
      if (date.toDateString() === tomorrow.toDateString()) return 'Ngày mai';
      return date.toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' });
    } catch {
      return props.date;
    }
  });

  const formattedTime = computed(() => {
    if (!props.startTime) return '';
    return props.endTime ? `${props.startTime} - ${props.endTime}` : props.startTime;
  });

  const formattedDateTime = computed(() => {
    const parts = [formattedDate.value, formattedTime.value].filter(Boolean);
    return parts.join(' ');
  });

  const participantsText = computed(() => {
    if (props.currentParticipants === undefined || props.maxParticipants === undefined) return '';
    return `${props.currentParticipants}/${props.maxParticipants}`;
  });

  const formattedPriceDisplay = computed(() => {
    if (!props.price && !props.originalPrice) return '';
    
    const parts = [];
    if (props.originalPrice) parts.push(formatPrice(props.originalPrice));
    if (props.price) parts.push(formatPrice(props.price));
    
    return {
      original: props.originalPrice ? formatPrice(props.originalPrice) : '',
      current: props.price ? formatPrice(props.price) : '',
      hasDiscount: !!(props.originalPrice && props.price && props.originalPrice > props.price)
    };
  });

  return {
    // State
    isFavorite,
    
    // Methods
    toggleFavorite,
    formatPrice,
    getBadgeStyles,
    
    // Computed
    formattedDate,
    formattedTime,
    formattedDateTime,
    participantsText,
    formattedPriceDisplay
  };
};
