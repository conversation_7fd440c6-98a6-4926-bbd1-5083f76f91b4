<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">{{ t('navigation.venues') }}</div>
    <div class="text-subtitle1 q-mb-lg text-grey-7">Tìm kiếm và đặt sân thể thao</div>

    <!-- Search and Filter Section -->
    <q-card class="q-mb-lg">
      <q-card-section>
        <q-input
          v-model="searchQuery"
          outlined
          :placeholder="t('venues.searchPlaceholder', 'Tìm kiếm sân thể thao...')"
          prepend-inner-icon="search"
          clearable
        />

        <div class="row q-gutter-md q-mt-md">
          <q-select
            v-model="selectedSport"
            :options="sportOptions"
            outlined
            label="Môn thể thao"
            clearable
            style="min-width: 150px"
          />

          <q-select
            v-model="selectedDistrict"
            :options="districtOptions"
            outlined
            label="Quận/Huyện"
            clearable
            style="min-width: 150px"
          />

          <PriceRangeSlider
            v-model="priceRange"
            :min="50000"
            :max="500000"
            :step="25000"
            label="Khoảng giá"
            style="min-width: 200px"
          />
        </div>
      </q-card-section>
    </q-card>

    <!-- Venues List -->
    <div class="row q-col-gutter-md">
      <div v-for="venue in filteredVenues" :key="venue.id" class="col-12 col-md-6 col-lg-4">
        <q-card class="venue-card cursor-pointer" @click="viewVenue(venue.id)">
          <q-img :src="venue.image" height="200px" class="venue-image">
            <div class="absolute-top-right q-pa-sm">
              <q-chip
                :color="venue.available ? 'positive' : 'negative'"
                text-color="white"
                size="sm"
              >
                {{ venue.available ? 'Còn trống' : 'Hết chỗ' }}
              </q-chip>
            </div>

            <div class="absolute-bottom-left q-pa-sm">
              <q-rating v-model="venue.rating" readonly size="16px" color="yellow" icon="star" />
            </div>
          </q-img>

          <q-card-section>
            <div class="text-h6 q-mb-xs">{{ venue.name }}</div>
            <div class="text-body2 text-grey-7 q-mb-sm">{{ venue.description }}</div>

            <div class="row items-center q-gutter-sm text-caption text-grey-6 q-mb-sm">
              <q-icon name="place" size="16px" />
              <span>{{ venue.address }}</span>
            </div>

            <div class="row items-center q-gutter-sm text-caption text-grey-6 q-mb-sm">
              <q-icon name="sports" size="16px" />
              <span>{{ venue.sports.join(', ') }}</span>
            </div>

            <div class="row items-center justify-between q-mt-md">
              <div>
                <div class="text-h6 text-primary">{{ formatPrice(venue.pricePerHour) }}/giờ</div>
                <div class="text-caption text-grey-6">{{ venue.distance }}km</div>
              </div>
              <q-btn
                color="primary"
                label="Đặt sân"
                size="sm"
                :disable="!venue.available"
                @click.stop="bookVenue(venue.id)"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredVenues.length === 0" class="text-center q-py-xl">
      <q-icon name="location_off" size="64px" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-6">Không tìm thấy sân nào</div>
      <div class="text-body2 text-grey-5">Thử thay đổi bộ lọc hoặc tìm kiếm khác</div>
    </div>

    <!-- Map Toggle Button -->
    <q-page-sticky position="bottom-left" :offset="[18, 18]">
      <q-btn fab icon="map" color="secondary" @click="toggleMapView" />
    </q-page-sticky>

    <!-- Add Venue Button -->
    <q-page-sticky position="bottom-right" :offset="[18, 18]">
      <q-btn fab icon="add_location" color="primary" @click="addVenue" />
    </q-page-sticky>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import PriceRangeSlider from 'src/components/common/PriceRangeSlider.vue';

const { t } = useI18n();
const $q = useQuasar();

// Reactive data
const searchQuery = ref('');
const selectedSport = ref<string | null>(null);
const selectedDistrict = ref<string | null>(null);
const priceRange = ref<string | null>(null);

// Filter options
const sportOptions = ['Bóng đá', 'Bóng rổ', 'Tennis', 'Cầu lông', 'Bóng chuyền', 'Bóng bàn'];

const districtOptions = [
  'Ba Đình',
  'Hoàn Kiếm',
  'Tây Hồ',
  'Long Biên',
  'Cầu Giấy',
  'Đống Đa',
  'Hai Bà Trưng',
  'Hoàng Mai',
  'Thanh Xuân',
];

// Sample venues data
const venues = ref([
  {
    id: 1,
    name: 'Sân bóng đá Mỹ Đình',
    description: 'Sân bóng đá cỏ nhân tạo chất lượng cao',
    address: 'Từ Liêm, Hà Nội',
    sports: ['Bóng đá'],
    pricePerHour: 300000,
    rating: 4.5,
    distance: 2.5,
    available: true,
    image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400',
  },
  {
    id: 2,
    name: 'Sân tennis Ciputra',
    description: 'Sân tennis tiêu chuẩn quốc tế với ánh sáng đầy đủ',
    address: 'Tây Hồ, Hà Nội',
    sports: ['Tennis'],
    pricePerHour: 250000,
    rating: 4.8,
    distance: 3.2,
    available: true,
    image: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400',
  },
  {
    id: 3,
    name: 'Nhà thi đấu cầu lông Trịnh Hoài Đức',
    description: 'Nhà thi đấu cầu lông với 8 sân thi đấu',
    address: 'Đống Đa, Hà Nội',
    sports: ['Cầu lông'],
    pricePerHour: 120000,
    rating: 4.2,
    distance: 1.8,
    available: false,
    image: 'https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?w=400',
  },
  {
    id: 4,
    name: 'Sân bóng rổ Thanh Xuân',
    description: 'Sân bóng rổ ngoài trời với không gian thoáng mát',
    address: 'Thanh Xuân, Hà Nội',
    sports: ['Bóng rổ'],
    pricePerHour: 150000,
    rating: 4.0,
    distance: 4.1,
    available: true,
    image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400',
  },
]);

// Computed properties
const filteredVenues = computed(() => {
  let filtered = venues.value;

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(
      (venue) =>
        venue.name.toLowerCase().includes(query) ||
        venue.description.toLowerCase().includes(query) ||
        venue.address.toLowerCase().includes(query),
    );
  }

  // Filter by sport
  if (selectedSport.value) {
    filtered = filtered.filter((venue) => venue.sports.includes(selectedSport.value!));
  }

  // Filter by district
  if (selectedDistrict.value) {
    filtered = filtered.filter((venue) => venue.address.includes(selectedDistrict.value!));
  }

  // Filter by price range
  if (priceRange.value) {
    const [min, max] = priceRange.value.split('-').map(Number);
    if (min !== undefined && max !== undefined) {
      filtered = filtered.filter((venue) => venue.pricePerHour >= min && venue.pricePerHour <= max);
    }
  }

  return filtered;
});

// Methods
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(price);
};

const viewVenue = (venueId: number) => {
  $q.notify({
    message: `Xem chi tiết sân ${venueId}`,
    color: 'primary',
  });
};

const bookVenue = (venueId: number) => {
  $q.notify({
    message: `Đặt sân ${venueId}`,
    color: 'positive',
  });
};

const toggleMapView = () => {
  $q.notify({
    message: 'Chuyển sang chế độ bản đồ',
    color: 'secondary',
  });
};

const addVenue = () => {
  $q.notify({
    message: 'Thêm sân mới',
    color: 'primary',
  });
};
</script>

<style lang="scss" scoped>
.venue-card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.venue-image {
  position: relative;
}
</style>
