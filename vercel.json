{"version": 2, "buildCommand": "node vercel-build.cjs", "outputDirectory": "dist/spa", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"NODE_ENV": "production", "QUASAR_CLI": "true", "DISABLE_VITE_BUILD": "true", "VITE_PUBLIC_FIREBASE_API_KEY": "AIzaSyAHl1BXTtjtNzG6yPbV_WnWlcdNmXGYIsY", "VITE_PUBLIC_FIREBASE_AUTH_DOMAIN": "tido-c9934.firebaseapp.com", "VITE_PUBLIC_FIREBASE_PROJECT_ID": "tido-c9934", "VITE_PUBLIC_FIREBASE_STORAGE_BUCKET": "tido-c9934.firebasestorage.app", "VITE_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "133426497446", "VITE_PUBLIC_FIREBASE_APP_ID": "1:133426497446:web:7112865e2422a966afb167", "VITE_PUBLIC_FIREBASE_MEASUREMENT_ID": "G-FMLB091P3R"}, "build": {"env": {"NODE_ENV": "production", "QUASAR_CLI": "true", "DISABLE_VITE_BUILD": "true", "VITE_PUBLIC_FIREBASE_API_KEY": "AIzaSyAHl1BXTtjtNzG6yPbV_WnWlcdNmXGYIsY", "VITE_PUBLIC_FIREBASE_AUTH_DOMAIN": "tido-c9934.firebaseapp.com", "VITE_PUBLIC_FIREBASE_PROJECT_ID": "tido-c9934", "VITE_PUBLIC_FIREBASE_STORAGE_BUCKET": "tido-c9934.firebasestorage.app", "VITE_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "133426497446", "VITE_PUBLIC_FIREBASE_APP_ID": "1:133426497446:web:7112865e2422a966afb167", "VITE_PUBLIC_FIREBASE_MEASUREMENT_ID": "G-FMLB091P3R"}}}