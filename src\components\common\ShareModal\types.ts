import type { Component } from 'vue';

export interface ShareOptions {
  url: string;
  title?: string;
  description?: string;
  hashtags?: string[];
  via?: string;
}

export interface SocialLink {
  icon: Component;
}

export type SharePlatform = 'facebook' | 'zalo' | 'instagram' | 'tiktok' | 'messenger';

export interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  url?: string;
}
