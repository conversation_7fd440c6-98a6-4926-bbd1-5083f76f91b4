{"editor.bracketPairColorization.enabled": true, "editor.codeActionsOnSave": ["source.fixAll.eslint"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.guides.bracketPairs": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"], "typescript.tsdk": "node_modules/typescript/lib", "[vue]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}}