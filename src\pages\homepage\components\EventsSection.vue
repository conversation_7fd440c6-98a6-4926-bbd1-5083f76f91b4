<template>
  <div class="events-section">
    <!-- Section Header -->
    <SectionHeader v-bind="headerProps" />
    
    <!-- Scrollable Events -->
    <q-scroll-area
      horizontal
      class="events-scroll"
      :thumb-style="{ display: 'none' }"
    >
      <div class="row no-wrap q-gutter-md events-container">
        <template v-if="events && events.length > 0">
          <slot name="events-list" :events="events">
            <div 
              v-for="(event, index) in events" 
              :key="event.id || index"
              class="event-item"
            >
              <EventCardVertical
                :title="event.title"
                :photoUrl="event.photoUrl"
                :badge="event.badge"
                :price="event.price"
                :originalPrice="event.originalPrice"
                :rating="event.rating"
                :date="event.date"
                :startTime="event.startTime"
                :endTime="event.endTime"
                :location="event.location"
                :distance="event.distance"
                :currentParticipants="event.currentParticipants"
                :maxParticipants="event.maxParticipants"
                @click="handleEventClick(event)"
                @book="handleEventBook(event)"
                @favorite="handleEventFavorite(event, $event)"
              />
            </div>
          </slot>
        </template>
        
        <!-- Empty State -->
        <template v-else>
          <div class="empty-state">
            <div class="empty-icon">
              <Calendar :size="48" color="#cbd5e1" />
            </div>
            <div class="empty-title">{{ emptyStateTitle || 'Không có sự kiện nào' }}</div>
            <div class="empty-subtitle">{{ emptyStateSubtitle || 'Hãy thử lại sau' }}</div>
          </div>
        </template>
      </div>
    </q-scroll-area>
  </div>
</template>

<script setup lang="ts">
import { Calendar } from 'lucide-vue-next';
import type { Component } from 'vue';
import { computed } from 'vue';
import SectionHeader from './SectionHeader.vue';
import EventCardVertical from 'src/components/EventCardVertical.vue';
import type { Event } from '../data/mockData';

interface Props {
  title: string;
  icon?: Component;
  badge?: string;
  events?: Event[];
  viewAllLink?: string;
  viewAllLabel?: string;
  emptyStateTitle?: string;
  emptyStateSubtitle?: string;
}

const props = withDefaults(defineProps<Props>(), {
  events: () => [],
  emptyStateTitle: 'Không có sự kiện nào',
  emptyStateSubtitle: 'Hãy thử lại sau'
});

// Create computed header props to avoid type errors
const headerProps = computed(() => ({
  title: props.title,
  icon: props.icon,
  badge: props.badge,
  viewAll: !!props.viewAllLink,
  viewAllLink: props.viewAllLink,
  viewAllLabel: props.viewAllLabel,
  showArrow: true
}));

const emit = defineEmits<{
  click: [event: Event];
  book: [event: Event];
  favorite: [event: Event, isFavorite: boolean];
}>();

const handleEventClick = (event: Event) => {
  emit('click', event);
};

const handleEventBook = (event: Event) => {
  emit('book', event);
};

const handleEventFavorite = (event: Event, isFavorite: boolean) => {
  emit('favorite', event, isFavorite);
};
</script>

<style lang="scss" scoped>
.events-section {
  margin-bottom: 24px;
  
  .events-scroll {
    .events-container {
      padding: 4px 0;
      
      .event-item {
        width: 200px;
        flex-shrink: 0;
      }
    }
  }
  
  .empty-state {
    width: 100%;
    padding: 32px 0;
    text-align: center;
    
    .empty-icon {
      margin-bottom: 16px;
    }
    
    .empty-title {
      font-size: 16px;
      font-weight: 600;
      color: #64748b;
      margin-bottom: 8px;
    }
    
    .empty-subtitle {
      font-size: 14px;
      color: #94a3b8;
    }
  }
}

// Mobile optimizations
@media (max-width: 599px) {
  .events-section {
    .events-scroll {
      margin: 0 -16px;
      
      .events-container {
        padding: 4px 16px;
      }
    }
  }
}
</style> 