import type { IActivity, ISkillLevel, ISport, IVenue } from 'src/types/activities';

export interface IActivitiesBannerProps {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
}

export interface IUpcomingEventProps extends Omit<Partial<IActivity>, 'skillLevels'> {
  skillLevels: Pick<ISkillLevel, 'id' | 'name' | 'sportId'>[];
  buttonText: string;
}

export interface ISportProps extends Pick<ISport, 'id' | 'name'> {
  icon: string;
}

export interface IVenueProps extends Pick<IVenue, 'id' | 'name' | 'address'> {
  photoUrl?: string;
  distance: number;
  price: string;
  rating?: number;
}
