// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #ff8142;
$secondary: #1976d2;
$accent: #fbbe47;

$dark: #242424;
$dark-page: #242424;

$positive: #10c239;
$negative: #f25e5e;
$info: #0db5d6;
$warning: #d48d53;

// Custom button border radius
$button-border-radius: 10px !default;
// $button-padding: 12px 24px !default;

// Custom card border radius
$card-border-radius: 16px !default;

// Disable button uppercase text transformation
// Try different Quasar variable names
$button-text-transform: none !default;
$generic-text-transform: none !default;
