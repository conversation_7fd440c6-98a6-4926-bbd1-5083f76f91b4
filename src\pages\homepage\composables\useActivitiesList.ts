import { storeToRefs } from 'pinia';
import { useActivitiesStore } from 'src/stores/activitiesStore';
import type { IActivity, IActivityQueryParams, ICreateActivity } from 'src/types/activities';
import { onMounted, ref } from 'vue';

export const useActivitiesList = () => {
  const activitiesStore = useActivitiesStore();
  const { activities, sports, skillLevels, tags, loading, error, pagination } =
    storeToRefs(activitiesStore);

  const currentPage = ref(1);
  const itemsPerPage = ref(10);
  const filters = ref<Partial<IActivityQueryParams>>({});
  const selectedActivity = ref<IActivity | null>(null);

  const loadActivities = async (page = currentPage.value) => {
    currentPage.value = page;
    const params: IActivityQueryParams = {
      page: currentPage.value,
      limit: itemsPerPage.value,
      ...filters.value,
    };
    await activitiesStore.getActivities(params);
  };

  const getActivityDetails = async (id: string) => {
    const activity = await activitiesStore.getActivity(id);
    selectedActivity.value = activity;
    return activity;
  };

  const createNewActivity = async (data: ICreateActivity) => {
    const newActivity = await activitiesStore.createActivity(data);
    await loadActivities(currentPage.value);
    return newActivity;
  };

  const uploadPhoto = async (activityId: string, file: File) => {
    const photoUrl = await activitiesStore.uploadActivityPhoto(activityId, file);
    return photoUrl;
  };

  const loadSkillLevels = async (sportId: string) => {
    await activitiesStore.getSkillLevels(sportId);
  };

  const loadTags = async () => {
    await activitiesStore.getTags();
  };

  const setFilters = (newFilters: Partial<IActivityQueryParams>) => {
    filters.value = { ...filters.value, ...newFilters };
    currentPage.value = 1;
    loadActivities(1);
  };

  const resetFilters = () => {
    filters.value = {};
    currentPage.value = 1;
    loadActivities(1);
  };

  const refreshActivities = () => {
    return loadActivities(currentPage.value);
  };

  onMounted(async () => {
    await loadActivities();
  });

  return {
    activities,
    sports,
    skillLevels,
    tags,
    loading,
    error,
    pagination,
    currentPage,
    itemsPerPage,
    filters,
    selectedActivity,

    loadActivities,
    getActivityDetails,
    createNewActivity,
    uploadPhoto,

    loadSkillLevels,
    loadTags,

    setFilters,
    resetFilters,
    refreshActivities,
  };
};
