<template>
  <div class="homepage-skeleton">
    <!-- Search Section Skeleton -->
    <div class="search-skeleton q-mb-md">
      <q-skeleton type="rect" height="48px" class="search-input-skeleton" />
    </div>

    <!-- Tabs Skeleton -->
    <div class="tabs-skeleton q-mb-sm">
      <div class="tab-buttons">
        <q-skeleton type="rect" width="120px" height="40px" class="tab-skeleton" />
        <q-skeleton type="rect" width="120px" height="40px" class="tab-skeleton" />
      </div>
    </div>

    <!-- Default Filter Values Skeleton -->
    <div class="default-filter-skeleton q-mb-lg">
      <div class="filter-tags-skeleton">
        <q-skeleton type="rect" width="100px" height="32px" class="filter-tag-skeleton active" />
        <q-skeleton type="rect" width="80px" height="32px" class="filter-tag-skeleton" />
        <q-skeleton type="rect" width="90px" height="32px" class="filter-tag-skeleton" />
      </div>
    </div>

    <!-- Joined Events Section Skeleton -->
    <div class="joined-events-skeleton q-mb-lg">
      <div class="section-header-skeleton q-mb-md">
        <q-skeleton type="text" width="140px" height="24px" />
      </div>
      <div class="joined-events-scroll">
        <div class="row no-wrap q-gutter-md">
          <div
            v-for="n in 3"
            :key="`joined-${n}`"
            class="joined-event-card-skeleton"
          >
            <q-skeleton type="rect" height="100px" class="q-mb-sm" />
            <q-skeleton type="text" width="80%" height="16px" class="q-mb-xs" />
            <q-skeleton type="text" width="60%" height="14px" class="q-mb-xs" />
            <q-skeleton type="text" width="40%" height="12px" />
          </div>
        </div>
      </div>
    </div>

    <!-- Nearby Events Section Skeleton -->
    <div class="nearby-events-skeleton q-mb-lg">
      <div class="section-header-skeleton q-mb-md">
        <div class="row items-center justify-between">
          <q-skeleton type="text" width="120px" height="24px" />
          <q-skeleton type="text" width="60px" height="20px" />
        </div>
      </div>
      <div class="events-list-skeleton">
        <div
          v-for="n in 4"
          :key="`event-${n}`"
          class="event-list-item-skeleton q-mb-md"
        >
          <div class="row q-gutter-md">
            <q-skeleton type="rect" width="80px" height="80px" />
            <div class="col">
              <q-skeleton type="text" width="90%" height="18px" class="q-mb-xs" />
              <q-skeleton type="text" width="70%" height="14px" class="q-mb-xs" />
              <q-skeleton type="text" width="50%" height="14px" class="q-mb-sm" />
              <div class="row items-center justify-between">
                <q-skeleton type="text" width="60px" height="16px" />
                <div class="row q-gutter-xs">
                  <q-skeleton type="circle" size="24px" />
                  <q-skeleton type="circle" size="24px" />
                  <q-skeleton type="circle" size="24px" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Venues Section Skeleton -->
    <div class="venues-skeleton">
      <div class="section-header-skeleton q-mb-md">
        <div class="row items-center justify-between">
          <q-skeleton type="text" width="120px" height="24px" />
          <q-skeleton type="text" width="60px" height="20px" />
        </div>
      </div>
      
      <!-- Distance Filter Skeleton -->
      <div class="distance-filter-skeleton q-mb-lg">
        <div class="row q-gutter-sm">
          <q-skeleton type="rect" width="60px" height="28px" />
          <q-skeleton type="rect" width="60px" height="28px" />
          <q-skeleton type="rect" width="60px" height="28px" />
          <q-skeleton type="rect" width="60px" height="28px" />
        </div>
      </div>

      <!-- Venues Grid Skeleton -->
      <div class="venues-grid-skeleton">
        <div
          v-for="n in 6"
          :key="`venue-${n}`"
          class="venue-card-skeleton"
        >
          <div class="row q-gutter-md">
            <q-skeleton type="rect" width="80px" height="80px" />
            <div class="col">
              <div class="row items-start justify-between q-mb-xs">
                <q-skeleton type="text" width="70%" height="18px" />
                <q-skeleton type="rect" width="40px" height="20px" />
              </div>
              <q-skeleton type="text" width="80%" height="14px" class="q-mb-xs" />
              <div class="row items-center justify-between">
                <q-skeleton type="text" width="60px" height="16px" />
                <div class="row q-gutter-xs">
                  <q-skeleton type="rect" width="20px" height="20px" />
                  <q-skeleton type="rect" width="20px" height="20px" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props or logic needed for skeleton
</script>

<style lang="scss" scoped>
.homepage-skeleton {
  padding: 0;
  background: white;

  .search-skeleton {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;

    .search-input-skeleton {
      border-radius: 24px;
    }
  }

  .tabs-skeleton {
    padding: 0 16px;
    background: white;
    border-bottom: 1px solid #e9ecef;

    .tab-buttons {
      display: flex;
      gap: 8px;

      .tab-skeleton {
        border-radius: 20px;
      }
    }
  }

  .default-filter-skeleton {
    padding: 8px 16px;
    background: white;
    border-bottom: 1px solid #e9ecef;

    .filter-tags-skeleton {
      display: flex;
      gap: 8px;
      align-items: center;

      .filter-tag-skeleton {
        border-radius: 20px;

        &.active {
          background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);
          border: 1.5px solid #ff8142;
        }
      }
    }
  }
  
  .joined-events-skeleton {
    padding: 0 16px;
    
    .joined-events-scroll {
      overflow-x: auto;
      
      .joined-event-card-skeleton {
        min-width: 160px;
        width: 160px;
        padding: 12px;
        border: 1px solid #f1f5f9;
        border-radius: 12px;
        
        :deep(.q-skeleton) {
          border-radius: 8px;
        }
      }
    }
  }
  
  .nearby-events-skeleton {
    padding: 0 16px;
    
    .events-list-skeleton {
      .event-list-item-skeleton {
        padding: 12px;
        border: 1px solid #f1f5f9;
        border-radius: 12px;
        
        :deep(.q-skeleton) {
          border-radius: 8px;
        }
      }
    }
  }
  
  .venues-skeleton {
    padding: 0 16px;
    
    .distance-filter-skeleton {
      :deep(.q-skeleton) {
        border-radius: 14px;
      }
    }
    
    .venues-grid-skeleton {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;
      
      .venue-card-skeleton {
        padding: 12px;
        border: 1px solid #f1f5f9;
        border-radius: 12px;
        
        :deep(.q-skeleton) {
          border-radius: 8px;
        }
      }
    }
  }
  
  .section-header-skeleton {
    padding: 0;
  }
}

// Responsive grid for venues
@media (min-width: 480px) {
  .venues-skeleton {
    .venues-grid-skeleton {
      grid-template-columns: repeat(2, 1fr);
      gap: 14px;
    }
  }
}

@media (min-width: 768px) {
  .homepage-skeleton {
    max-width: 1200px;
    margin: 0 auto;
  }

  .search-skeleton {
    padding: 16px 24px;
  }

  .tabs-skeleton,
  .default-filter-skeleton,
  .joined-events-skeleton,
  .nearby-events-skeleton,
  .venues-skeleton {
    padding: 0 24px;
  }

  .default-filter-skeleton {
    padding: 8px 24px;
  }
  
  .venues-skeleton {
    .venues-grid-skeleton {
      grid-template-columns: repeat(3, 1fr);
      gap: 18px;
    }
  }
}

@media (min-width: 1024px) {
  .search-skeleton {
    padding: 16px 32px;
  }

  .tabs-skeleton,
  .default-filter-skeleton,
  .joined-events-skeleton,
  .nearby-events-skeleton,
  .venues-skeleton {
    padding: 0 32px;
  }

  .default-filter-skeleton {
    padding: 8px 32px;
  }
  
  .venues-skeleton {
    .venues-grid-skeleton {
      gap: 20px;
    }
  }
}
</style>
