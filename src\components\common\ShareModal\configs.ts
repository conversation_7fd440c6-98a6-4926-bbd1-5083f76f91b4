import type { ShareOptions, SharePlatform } from './types';

const TIDO_TITLE = 'TIDO';
const TIDO_HASHTAG = '#TIDO';
const SHARE_URL_TEMPLATES = {
  facebook: (options: ShareOptions) => {
    const params = new URLSearchParams({
      u: options.url,
      quote: options.description || '',
      hashtag: TIDO_HASHTAG,
    });
    return `https://www.facebook.com/sharer/sharer.php?${params}`;
  },

  zalo: (options: ShareOptions) => {
    const params = new URLSearchParams({
      url: options.url,
      title: options.title || TIDO_TITLE,
      d: options.description || '',
    });
    return `https://zalo.me/article/share?${params}`;
  },

  instagram: (options: ShareOptions) => {
    return `https://www.instagram.com/share?url=${options.url}`;
  },

  tiktok: (options: ShareOptions) => {
    return `https://www.tiktok.com/share?url=${options.url}`;
  },

  messenger: (options: ShareOptions) => {
    const params = new URLSearchParams({
      link: options.url,
      app_id: '123456789',
    });
    return `https://www.facebook.com/dialog/send?${params}`;
  },
} as const;

export const getShareUrl = (platform: SharePlatform, options: ShareOptions): string => {
  const template = SHARE_URL_TEMPLATES[platform as keyof typeof SHARE_URL_TEMPLATES];
  if (!template) return '';

  return template({
    ...options,
    url: options.url,
  });
};

export const openShareWindow = (url: string) => {
  if (!url) {
    console.warn('No share URL provided');
    return;
  }
  window.open(url, '_blank', 'width=800,height=600');
};
