import { computed, ref } from 'vue';

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

export function useLoadingState(initialLoading = false) {
  const state = ref<LoadingState>({
    isLoading: initialLoading,
    error: null,
    success: false,
  });

  const isLoading = computed(() => state.value.isLoading);
  const hasError = computed(() => !!state.value.error);
  const isSuccess = computed(() => state.value.success);
  const errorMessage = computed(() => state.value.error);

  function setLoading(loading: boolean) {
    state.value.isLoading = loading;
    if (loading) {
      state.value.error = null;
      state.value.success = false;
    }
  }

  function setError(error: string | Error | null) {
    state.value.error = error instanceof Error ? error.message : error;
    state.value.isLoading = false;
    state.value.success = false;
  }

  function setSuccess(success = true) {
    state.value.success = success;
    state.value.isLoading = false;
    state.value.error = null;
  }

  function reset() {
    state.value = {
      isLoading: false,
      error: null,
      success: false,
    };
  }

  async function execute<T>(
    asyncFn: () => Promise<T>,
    options: {
      onSuccess?: (result: T) => void;
      onError?: (error: Error) => void;
      resetOnStart?: boolean;
    } = {},
  ): Promise<T | null> {
    const { onSuccess, onError, resetOnStart = true } = options;

    if (resetOnStart) reset();
    setLoading(true);

    try {
      const result = await asyncFn();
      setSuccess();
      onSuccess?.(result);
      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      setError(err);
      onError?.(err);
      return null;
    }
  }

  return {
    // State
    state: state.value,

    // Computed
    isLoading,
    hasError,
    isSuccess,
    errorMessage,

    // Actions
    setLoading,
    setError,
    setSuccess,
    reset,
    execute,
  };
}
