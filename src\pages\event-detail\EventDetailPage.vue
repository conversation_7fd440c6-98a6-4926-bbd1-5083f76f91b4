<template>
  <SkeletonDetailPage v-if="isLoading" />
  <DetailCard v-else :item="mockData">
    <!-- Enhanced Tags Section -->
    <template #tags>
      <div class="q-mb-sm">
        <q-chip v-for="skill in mockData.skillLevels" :key="skill" color="grey-2" class="text-weight-medium" size="12px"
          icon="tag" style="border-radius: 4px; margin-top: -10px;">
          {{ skill }}
        </q-chip>
      </div>
    </template>

    <!-- Refactored Participants Section -->
    <template #participants>
      <div class="participants-section q-pa-md">
        <!-- Header with Icon and Count -->
        <div class="row items-center justify-between q-mb-md">
          <div class="row items-center">
            <q-icon name="group" size="18px" color="primary" class="q-mr-sm" />
            <span class="text-subtitle2 text-weight-bold text-grey-9">Participants</span>
            <q-badge
              color="primary"
              :label="`${mockData.participants.length}/${mockData.maxSlots}`"
              class="q-ml-sm"
              rounded
            />
          </div>
          <span class="text-body2 text-grey-6">
            {{ mockData.maxSlots - mockData.participants.length }} spots left
          </span>
        </div>

        <!-- Participants List -->
        <div class="participants-content">
          <div class="row items-center justify-between">
            <!-- Avatar Group -->
            <div class="avatar-group row items-center">
              <q-avatar
                v-for="(participant, index) in mockData.participants.slice(0, 4)"
                :key="participant.id"
                :size="$q.screen.lt.sm ? '36px' : '40px'"
                class="participant-avatar"
                :style="{
                  marginLeft: index === 0 ? '0' : '-8px',
                  zIndex: 10 - index
                }"
              >
                <img :src="participant.avatar" :alt="participant.name" />
                <q-tooltip class="bg-grey-9 text-white">
                  {{ participant.name }}
                </q-tooltip>
              </q-avatar>

              <!-- More participants indicator -->
              <q-avatar
                v-if="mockData.participants.length > 4"
                :size="$q.screen.lt.sm ? '36px' : '40px'"
                color="grey-3"
                text-color="grey-7"
                class="participant-avatar"
                style="margin-left: -8px; z-index: 1"
              >
                <span class="text-weight-bold" :class="$q.screen.lt.sm ? 'text-caption' : 'text-body2'">
                  +{{ mockData.participants.length - 4 }}
                </span>
              </q-avatar>
            </div>

            <!-- View All Button -->
            <q-btn
              flat
              no-caps
              color="primary"
              size="sm"
              class="view-all-btn"
              dense
              @click="showAllParticipants"
            >
              <span class="text-body2">View All</span>
              <q-icon name="chevron_right" size="14px" class="q-ml-xs" />
            </q-btn>
          </div>

          <!-- Participants Names (Mobile Only) -->
          <div class="q-mt-sm">
            <div class="text-caption text-grey-7">
              {{ formatParticipantNames(mockData.participants) }}
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Optimized Description Section -->
    <template #description>
      <div class="description-section">
        <!-- Enhanced Content -->
        <div class="description-content-compact">
          <p class="text-body1 text-grey-8">
            {{ displayedText }}
            <q-btn v-if="isLongText" flat no-caps small dense color="primary" @click="toggleShowMore">
              <span class="text-body2">{{ showFullText ? 'Thu gọn' : 'Xem thêm' }}</span>
              <q-icon :name="showFullText ? 'expand_less' : 'expand_more'" size="16px" class="q-ml-xs" />
            </q-btn>
          </p>
        </div>
      </div>
    </template>
    <!-- Event Details Section -->
    <template #eventDetails>
      <div class="event-details-section" :class="$q.screen.lt.sm ? 'q-pa-sm' : 'q-pa-md'">
        <div class="column" :class="$q.screen.lt.sm ? 'q-gutter-sm' : 'q-gutter-md'">
          <!-- Location -->
          <div class="row items-center justify-between">
            <span class="text-body2 text-grey-6">Địa điểm</span>
            <div class="row items-center q-gutter-xs">
              <q-btn
                flat
                round
                dense
                color="primary"
                size="sm"
                @click="openDirections"
              >
                <q-icon name="directions" size="18px" />
              </q-btn>
              <span class="text-body1 text-weight-medium text-grey-9">{{ mockData.location }}</span>
            </div>
          </div>
           <!-- Price -->
          <div class="row items-center justify-between">
            <span class="text-body2 text-grey-6">Phí tham gia</span>
            <span class="text-body1 text-weight-medium">{{ formatPrice(mockData.price) }}</span>
          </div>

          <!-- Date -->
          <div class="row items-center justify-between">
            <span class="text-body2 text-grey-6">Ngày diễn ra</span>
            <span class="text-body1 text-weight-medium text-grey-9">{{ formatDate(mockData.date) }}</span>
          </div>

          <!-- Time -->
          <div class="row items-center justify-between">
            <span class="text-body2 text-grey-6">Thời gian</span>
            <span class="text-body1 text-weight-medium text-grey-9">{{ mockData.startTime }} - {{ mockData.endTime }}</span>
          </div>

         
        </div>
      </div>
    </template>

    <!-- Organizer Section -->
    <template #organizer>
      <div class="Organizer-information q-my-md">
        <div class="d-flex items-center row justify-between">
          <div class="d-flex items-center row q-gutter-x-sm">
            <q-avatar size="48px" class="Organizer-avatar">
              <img :src="mockData.host.image" />
            </q-avatar>
            <div class="Organizer-info">
              <div class="Organizer-name text-weight-bold text-grey-9">{{ mockData.host.name }}</div>
              <div class="Organizer-distance text-body2 text-grey-6">{{ mockData.host.role }}</div>
            </div>
          </div>
          <div class="Organizer-actions q-gutter-x-lg">
            <q-btn flat no-caps dense color="primary" @click="handleCall">
              <PhoneForwarded :size="24" color="#ff8142" />
            </q-btn>
            <q-btn flat no-caps dense color="primary" @click="handleMessage">
              <MessageCircleMore :size="24" color="#ff8142" />
            </q-btn>
          </div>
        </div>
      </div>
    </template>

    <!-- Responsive Footer Actions -->
    <template #footerActions>
      <div class="footer-actions-container">
        <div class="flex row items-center justify-between full-width q-gutter-x-md">
          <!-- Price Section -->
          <div class="row items-baseline">
            <span
              :class="$q.screen.lt.sm ? 'text-h5' : 'text-h4'"
              class="text-weight-bold text-primary"
            >
              {{ formatPrice(mockData.price) }}
            </span>
            <span class="text-body2 text-grey-6 q-ml-xs">/người</span>
          </div>

          <!-- Action Button -->
          <q-btn
            :color="mockData.participants.length >= mockData.maxSlots ? 'grey-5' : 'primary'"
            :disable="mockData.participants.length >= mockData.maxSlots"
            :size="$q.screen.lt.sm ? 'md' : 'lg'"
            no-caps
            @click="handleRegister"
          >
            {{ mockData.participants.length >= mockData.maxSlots ? 'Đã đầy' : 'Đăng ký tham gia' }}
          </q-btn>
        </div>
      </div>
    </template>
  </DetailCard>

  <!-- Participants Dialog -->
  <q-dialog v-model="participantsDialog" position="bottom">
    <ParticipantsDialog :participants="participantsWithPhone" @hide="participantsDialog = false" />
  </q-dialog>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import DetailCard from 'src/pages/event-detail/components/DetailCard.vue';
import SkeletonDetailPage from 'src/pages/event-detail/components/SkeletonDetailPage.vue';
import ParticipantsDialog from 'src/pages/events/components/dialogs/ParticipantsDialog.vue';
import { computed, onMounted, ref } from 'vue';
import { MessageCircleMore, PhoneForwarded } from 'lucide-vue-next';

const $q = useQuasar();

// Reactive state
const isLoading = ref(true);
const showFullText = ref(false);
const participantsDialog = ref(false);

// Constants
const maxLength = 100;

// Computed properties
const displayedText = computed(() => {
  if (showFullText.value || mockData.value.description.length <= maxLength) {
    return mockData.value.description;
  }
  return mockData.value.description.slice(0, maxLength) + '...';
});

const isLongText = computed(() => {
  return mockData.value.description.length > maxLength;
});

// Format participants data for the dialog (add phone numbers)
const participantsWithPhone = computed(() => {
  return mockData.value.participants.map((participant) => ({
    ...participant,
    phone: `+84 ${Math.floor(Math.random() * 900000000) + 100000000}` // Mock phone numbers
  }));
});

const openDirections = () => {
  const address = encodeURIComponent(mockData.value.location);
  const url = `https://www.google.com/maps/search/?api=1&query=${address}`;
  window.open(url, '_blank');
};

// Lifecycle
onMounted(async () => {
  await new Promise((resolve) => setTimeout(resolve, 1500));
  isLoading.value = false;
});

// Methods
const toggleShowMore = () => {
  showFullText.value = !showFullText.value;
};



const handleCall = () => {
  window.open(`tel:${mockData.value.host.phone}`, '_blank');
};

const handleMessage = () => {
  const phone = mockData.value.host.phone.replace(/\D/g, '');
  window.open(`https://zalo.me/${phone}`, '_blank');
};

const handleRegister = () => {
  $q.dialog({
    title: 'Xác nhận đăng ký',
    message: `Bạn có muốn đăng ký tham gia sự kiện "${mockData.value.title}"?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    $q.notify({
      message: 'Đăng ký thành công!',
      color: 'positive',
      position: 'top',
      timeout: 2000,
    });
  });
};

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(price);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};



const showAllParticipants = () => {
  participantsDialog.value = true;
};

const formatParticipantNames = (participants: any[]) => {
  const names = participants.map(p => p.name);
  if (names.length <= 2) {
    return names.join(', ');
  }
  return `${names[0]} và ${participants.length - 1} người khác`;
};

const mockData = ref({
  id: 1,
  title: 'Tuyển giao lưu sân cầu lông Phan bá vành',
  rating: 4.5,
  description:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. ' +
    'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. ' +
    'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. ' +
    'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
  date: '2023-05-15',
  startTime: '11:00 PM',
  endTime: '12:00 AM',
  price: 120000,
  skillLevels: ['Beginner', 'Intermediate', 'Advanced'],
  tags: ['Badminton', 'tuyển giao lưu', 'Vui vẻ'],
  maxSlots: 100,
  slotsRemaining: 17,
  location: 'Sân Mỹ Đình • 1.2 km',
  photoUrl:
    'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-tXZUvtSdVMCgmijjspMdqUiuAn6JRA.png',
  participants: [
    {
      id: 1,
      name: 'Người tham gia 1',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    },
    {
      id: 2,
      name: 'Người tham gia 2',
      avatar:
        'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-tXZUvtSdVMCgmijjspMdqUiuAn6JRA.png',
    },
    {
      id: 3,
      name: 'Người tham gia 3',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
    },
  ],
  host: {
    id: 1,
    name: 'SonicVibe Events',
    role: 'Organize Team',
    phone: '0909090909',
    image:
      'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-tXZUvtSdVMCgmijjspMdqUiuAn6JRA.png',
  },
});
</script>

<style scoped lang="scss">
.footer-actions-container {
  width: 100%;
}
// Participants Section - Only essential styles
.participants-section {
  background: #f8f9fa;
  border-radius: 12px;

  .participant-avatar {
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px) scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 20 !important;
    }

    img {
      object-fit: cover;
    }
  }

  .view-all-btn {
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(#ff8142, 0.08);
    }
  }
}

// Event Details Section - Using Quasar CSS
.event-details-section {
  background: #f8f9fa;
  border-radius: 12px;
}

// Mobile Responsive - Using Quasar responsive classes
.participants-section .participant-avatar:hover {
  transform: translateY(-1px) scale(1.03);
}
</style>
