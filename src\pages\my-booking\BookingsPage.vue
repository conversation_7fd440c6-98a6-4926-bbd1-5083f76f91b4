<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">{{ t('navigation.bookings') }}</div>
    <div class="text-subtitle1 q-mb-lg text-grey-7">Qu<PERSON><PERSON> lý các đặt chỗ của bạn</div>

    <!-- Status Filter Tabs -->
    <q-tabs
      v-model="activeTab"
      class="text-grey q-mb-lg"
      active-color="primary"
      indicator-color="primary"
      align="justify"
      narrow-indicator
    >
      <q-tab name="all" label="Tất cả" />
      <q-tab name="upcoming" label="Sắp tới" />
      <q-tab name="completed" label="Đã hoàn thành" />
      <q-tab name="cancelled" label="Đã hủy" />
    </q-tabs>

    <!-- Bookings List -->
    <div class="q-gutter-md">
      <q-card
        v-for="booking in filteredBookings"
        :key="booking.id"
        class="booking-card"
        :class="getBookingCardClass(booking.status)"
      >
        <q-card-section class="row items-start q-gutter-md">
          <!-- Booking Image -->
          <q-img :src="booking.venue.image" width="80px" height="80px" class="rounded-borders" />

          <!-- Booking Details -->
          <div class="col">
            <div class="row items-center justify-between q-mb-xs">
              <div class="text-h6">{{ booking.venue.name }}</div>
              <q-chip :color="getStatusColor(booking.status)" text-color="white" size="sm">
                {{ getStatusLabel(booking.status) }}
              </q-chip>
            </div>

            <div class="text-body2 text-grey-7 q-mb-sm">{{ booking.venue.address }}</div>

            <div class="row items-center q-gutter-md text-caption text-grey-6 q-mb-sm">
              <div class="row items-center q-gutter-xs">
                <q-icon name="event" size="16px" />
                <span>{{ formatDate(booking.date) }}</span>
              </div>

              <div class="row items-center q-gutter-xs">
                <q-icon name="schedule" size="16px" />
                <span>{{ booking.timeSlot }}</span>
              </div>

              <div class="row items-center q-gutter-xs">
                <q-icon name="group" size="16px" />
                <span>{{ booking.participants }} người</span>
              </div>
            </div>

            <div class="row items-center justify-between">
              <div class="text-h6 text-primary">{{ formatPrice(booking.totalPrice) }}</div>

              <div class="row q-gutter-xs">
                <q-btn
                  v-if="booking.status === 'upcoming'"
                  size="sm"
                  color="negative"
                  outline
                  label="Hủy"
                  @click="cancelBooking(booking.id)"
                />

                <q-btn
                  v-if="booking.status === 'upcoming'"
                  size="sm"
                  color="primary"
                  outline
                  label="Sửa"
                  @click="editBooking(booking.id)"
                />

                <q-btn
                  size="sm"
                  color="primary"
                  label="Chi tiết"
                  @click="viewBookingDetails(booking.id)"
                />
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Empty State -->
    <div v-if="filteredBookings.length === 0" class="text-center q-py-xl">
      <q-icon name="event_busy" size="64px" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-6">Không có đặt chỗ nào</div>
      <div class="text-body2 text-grey-5">
        {{
          activeTab === 'all'
            ? 'Bạn chưa có đặt chỗ nào'
            : `Không có đặt chỗ ${getTabLabel(activeTab)}`
        }}
      </div>
      <q-btn
        v-if="activeTab === 'all'"
        color="primary"
        label="Đặt sân ngay"
        class="q-mt-md"
        @click="goToVenues"
      />
    </div>

    <!-- Floating Action Button -->
    <q-page-sticky position="bottom-right" :offset="[18, 18]">
      <q-btn fab icon="add" color="primary" @click="createBooking" />
    </q-page-sticky>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const $q = useQuasar();
const router = useRouter();

// Reactive data
const activeTab = ref('all');

type BookingStatus = 'upcoming' | 'completed' | 'cancelled';

type Booking = {
  id: number;
  venue: {
    name: string;
    address: string;
    image: string;
  };
  date: Date;
  timeSlot: string;
  participants: number;
  totalPrice: number;
  status: BookingStatus;
  bookingCode: string;
};

// Sample bookings data
const bookings = ref<Booking[]>([
  {
    id: 1,
    venue: {
      name: 'Sân bóng đá Mỹ Đình',
      address: 'Từ Liêm, Hà Nội',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400',
    },
    date: new Date('2024-02-15T19:00:00'),
    timeSlot: '19:00 - 21:00',
    participants: 22,
    totalPrice: 600000,
    status: 'upcoming',
    bookingCode: 'TD001',
  },
  {
    id: 2,
    venue: {
      name: 'Sân tennis Ciputra',
      address: 'Tây Hồ, Hà Nội',
      image: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400',
    },
    date: new Date('2024-02-10T08:00:00'),
    timeSlot: '08:00 - 10:00',
    participants: 4,
    totalPrice: 500000,
    status: 'completed',
    bookingCode: 'TD002',
  },
  {
    id: 3,
    venue: {
      name: 'Sân cầu lông Trịnh Hoài Đức',
      address: 'Đống Đa, Hà Nội',
      image: 'https://images.unsplash.com/photo-1626224583764-f87db24ac4ea?w=400',
    },
    date: new Date('2024-02-08T20:00:00'),
    timeSlot: '20:00 - 22:00',
    participants: 4,
    totalPrice: 240000,
    status: 'cancelled',
    bookingCode: 'TD003',
  },
]);

// Computed properties
const filteredBookings = computed(() => {
  if (activeTab.value === 'all') {
    return bookings.value;
  }
  return bookings.value.filter((booking) => booking.status === activeTab.value);
});

// Methods
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
};

const formatPrice = (price: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(price);
};

const getStatusColor = (status: BookingStatus) => {
  switch (status) {
    case 'upcoming':
      return 'primary';
    case 'completed':
      return 'positive';
    case 'cancelled':
      return 'negative';
    default:
      return 'grey';
  }
};

const getStatusLabel = (status: BookingStatus) => {
  switch (status) {
    case 'upcoming':
      return 'Sắp tới';
    case 'completed':
      return 'Hoàn thành';
    case 'cancelled':
      return 'Đã hủy';
    default:
      return 'Không xác định';
  }
};

const getBookingCardClass = (status: BookingStatus) => {
  switch (status) {
    case 'cancelled':
      return 'booking-cancelled';
    default:
      return '';
  }
};

const getTabLabel = (tab: string) => {
  switch (tab) {
    case 'upcoming':
      return 'sắp tới';
    case 'completed':
      return 'đã hoàn thành';
    case 'cancelled':
      return 'đã hủy';
    default:
      return '';
  }
};

const cancelBooking = (bookingId: number) => {
  $q.dialog({
    title: 'Xác nhận hủy đặt chỗ',
    message: 'Bạn có chắc chắn muốn hủy đặt chỗ này không?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const booking = bookings.value.find((b) => b.id === bookingId);
    if (booking) {
      booking.status = 'cancelled';
      $q.notify({
        message: 'Đã hủy đặt chỗ thành công',
        color: 'positive',
      });
    }
  });
};

const editBooking = (bookingId: number) => {
  $q.notify({
    message: `Chỉnh sửa đặt chỗ ${bookingId}`,
    color: 'primary',
  });
};

const viewBookingDetails = (bookingId: number) => {
  $q.notify({
    message: `Xem chi tiết đặt chỗ ${bookingId}`,
    color: 'primary',
  });
};

const createBooking = () => {
  router.push('/venues');
};

const goToVenues = () => {
  router.push('/venues');
};
</script>

<style lang="scss" scoped>
.booking-card {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.booking-cancelled {
    opacity: 0.7;
  }
}
</style>
