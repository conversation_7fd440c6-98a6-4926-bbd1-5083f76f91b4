<template>
  <q-page class="row items-center justify-evenly">
    <!-- All these will have 42px height -->
    <q-input outlined placeholder="Placeholder" v-model="text" label="Text Input" :dense="dense" />
    <q-select
      dense
      outlined
      behavior="menu"
      v-model="selected"
      :options="['Option 1', 'Option 2', 'Option 3']"
      label="Select"
    />
    <q-file dense outlined v-model="file" label="File Upload" />

    <q-btn-toggle
      v-model="model"
      class="my-custom-toggle"
      no-caps
      rounded
      toggle-color="primary"
      color="white"
      text-color="primary"
      :options="[
        { label: 'Option 1', value: 'one' },
        { label: 'Option 2', value: 'two' },
      ]"
    />

    <q-input v-model="text" outlined type="textarea" />

    <!-- Textarea grows but starts at 42px -->
    <q-textarea outlined v-model="message" label="Message" />
    <div class="q-pa-md q-gutter-sm">
      <q-btn color="white" text-color="black" label="Standard" />
      <q-btn round color="primary" icon="shopping_cart" />
      <q-btn color="secondary" label="Secondary" />
    </div>
    <q-card class="my-card">
      <q-video src="https://www.youtube.com/embed/k3_tw44QsZQ?rel=0" />

      <q-card-section>
        <div class="text-h6">Our Changing Planet</div>
        <div class="text-subtitle2">by John Doe</div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut
        labore et dolore magna aliqua.
      </q-card-section>
    </q-card>
    <div class="q-pa-md q-gutter-md">
      <div>
        <q-chip icon="event">Add to calendar</q-chip>
        <q-chip icon="bookmark">Bookmark</q-chip>
        <q-chip icon="alarm" label="Set alarm" />
        <q-chip class="glossy" icon="directions">Get directions</q-chip>
      </div>
      <div>
        <q-chip color="primary" text-color="white" icon="event"> Add to calendar </q-chip>
        <q-chip color="teal" text-color="white" icon="bookmark"> Bookmark </q-chip>
        <q-chip class="glossy" color="orange" text-color="white" icon-right="star"> Star </q-chip>
        <q-chip color="red" text-color="white" icon="alarm" label="Set alarm" />
        <q-chip color="deep-orange" text-color="white" icon="directions"> Get directions </q-chip>
        <q-chip>
          <q-avatar icon="bookmark" color="red" text-color="white" />
          Bookmark
        </q-chip>
        <q-chip>
          <q-avatar color="red" text-color="white">50</q-avatar>
          Emails
        </q-chip>
        <q-chip>
          <q-avatar>
            <img src="https://cdn.quasar.dev/img/avatar5.jpg" />
          </q-avatar>
          John
        </q-chip>
      </div>
    </div>
  </q-page>
  <template> </template>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Add these missing reactive variables
const text = ref('');
const selected = ref('');
const file = ref(null);
const model = ref('one');
const message = ref('');
const dense = ref(false);
</script>
