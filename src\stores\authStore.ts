import { useLocalStorage } from '@vueuse/core';
import { AxiosError } from 'axios';
import { defineStore } from 'pinia';
import { useQuasar } from 'quasar';
import { useFirebaseAuth } from 'src/composables/auth/useFirebaseAuth';
import { LOCAL_STORAGE_KEYS } from 'src/constants';
import { authService } from 'src/services/authService';
import type { IErrorResponse, LoginResponseData } from 'src/types/auth';
import { computed, ref } from 'vue';

export type AuthState = {
  loading: boolean;
  error: IErrorResponse['error'] | null;
  isOnboarded: boolean;
  isIntroSeen: boolean;
};

export const useAuthStore = defineStore('auth', () => {
  const firebaseAuth = useFirebaseAuth();
  const $q = useQuasar();

  // user represents your API's user object with additional fields
  const loading = ref(false);
  const error = ref<AuthState['error']>(null);
  const tokenRef = useLocalStorage(LOCAL_STORAGE_KEYS.TOKEN, '');
  const refreshTokenRef = useLocalStorage(LOCAL_STORAGE_KEYS.REFRESH_TOKEN, '');

  // Getters
  const isAuthenticated = computed(() => firebaseAuth.isAuthenticated);
  const needsOnboarding = computed(() => isAuthenticated.value);

  // Handle auth response
  const handleAuthResponse = (response: { data: LoginResponseData }) => {
    tokenRef.value = response.data.customToken;
    refreshTokenRef.value = response.data.refreshToken;
  };

  // Actions
  const loginWithGoogle = async () => {
    const firebaseResult = await firebaseAuth.signInWithGoogle();
    try {
      const response = await authService.login({ idToken: firebaseResult.idToken });
      handleAuthResponse(response);
    } catch (err) {
      console.error(err, 'Failed to login');
    }
  };

  const loginWithEmail = async (email: string, password: string) => {
    const firebaseResult = await firebaseAuth.signInWithEmail(email, password);
    try {
      const response = await authService.login({ idToken: firebaseResult.idToken });
      handleAuthResponse(response);
    } catch (err) {
      console.error(err, 'Failed to login');
    }
  };

  const registerWithGoogle = async () => {
    const firebaseResult = await firebaseAuth.signInWithGoogle();
    try {
      const response = await authService.register({
        idToken: firebaseResult.idToken,
        displayName: firebaseResult.user.displayName ?? '',
        phoneNumber: firebaseResult.user.phoneNumber ?? '',
      });
      console.log('response', response);
      handleAuthResponse(response);
    } catch (err) {
      if (err instanceof AxiosError && err.response?.status === 409) {
        $q.notify({
          type: 'negative',
          message: 'User already exists',
          position: 'top',
        });
      }
      console.error(err, 'Failed to register');
    }
  };

  const registerWithEmail = async (email: string, password: string, displayName: string) => {
    const firebaseResult = await firebaseAuth.signUpWithEmail(email, password);
    try {
      const response = await authService.register({
        idToken: firebaseResult.idToken,
        displayName: displayName,
      });
      handleAuthResponse(response);
    } catch (err) {
      console.error(err, 'Failed to register');
    }
  };

  const clearAuth = async () => {
    await firebaseAuth.signOut();
    tokenRef.value = '';
    refreshTokenRef.value = '';
  };

  return {
    // State
    loading,
    error,
    firebaseUser: firebaseAuth.firebaseUser,

    // Getters
    isAuthenticated,
    needsOnboarding,

    // Actions
    loginWithGoogle,
    loginWithEmail,
    registerWithEmail,
    registerWithGoogle,
    clearAuth,
  };
});
