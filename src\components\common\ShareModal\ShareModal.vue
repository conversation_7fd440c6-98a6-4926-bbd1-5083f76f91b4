<template>
  <q-dialog v-model="isDialogOpen" position="standard">
    <q-card class="share-modal" style="width: 600px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Chia sẻ ngay</div>
        <q-space />
        <q-btn icon="close" flat round dense @click="onClose" />
      </q-card-section>

      <q-card-section>
        <div class="row items-center q-mb-md">
          <div class="col">
            <q-input
              v-model="shareLink"
              outlined
              readonly
              class="share-link-input"
              bg-color="grey-2"
            >
              <template v-slot:append>
                <q-btn color="primary" label="Sao chép" @click="copyLink" />
              </template>
            </q-input>
          </div>
        </div>

        <div class="text-subtitle2 q-mb-sm">Chia sẻ link qua</div>
        <div class="row q-gutter-md justify-start">
          <q-btn
            v-for="platform in platforms"
            :key="platform"
            round
            color="white"
            @click="shareToSocial(platform)"
          >
            <q-img
              :src="`/images/social/${platform}.svg`"
              width="36px"
              height="36px"
              fit="contain"
              :alt="platform"
            />
          </q-btn>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { ref, watch } from 'vue';

import { getShareUrl, openShareWindow } from './configs';
import type { ShareModalProps, SharePlatform } from './types';

const props = defineProps<ShareModalProps>();
const $q = useQuasar();

const isDialogOpen = ref(props.isOpen);
const shareLink = ref(props.url || window.location.href);

// List of available social platforms
const platforms: SharePlatform[] = ['facebook', 'zalo', 'instagram', 'tiktok', 'messenger'];

// Watch for changes in isOpen prop
watch(
  () => props.isOpen,
  (newVal) => {
    isDialogOpen.value = newVal;
  },
);

// Watch for dialog close and notify parent
watch(isDialogOpen, (newVal) => {
  if (!newVal && props.isOpen) {
    props.onClose();
  }
});

// Watch for URL changes
watch(
  () => props.url,
  (newVal) => {
    if (newVal) {
      shareLink.value = newVal;
    }
  },
);

// Copy link to clipboard
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value);
    $q.notify({
      color: 'positive',
      message: 'Link copied to clipboard',
      position: 'top',
      timeout: 1500,
    });
  } catch (err) {
    console.error('Failed to copy link', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to copy link',
      position: 'top',
    });
  }
};

// Share to social media
const shareToSocial = (platform: SharePlatform) => {
  const shareOptions = {
    url: shareLink.value,
    title: 'Check out this event',
    description: 'I found this amazing event. Check it out!',
  };

  const shareUrl = getShareUrl(platform, shareOptions);
  if (shareUrl) {
    openShareWindow(shareUrl);
  } else {
    $q.notify({
      color: 'warning',
      message: `Sharing to ${platform} is not supported yet`,
      position: 'top',
    });
  }
};
</script>

<style lang="scss" scoped>
.share-modal {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.share-link-input {
  width: 100%;
}
</style>
