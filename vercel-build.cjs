#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Vercel build process...');

// Check Node.js version
console.log(`📋 Node.js version: ${process.version}`);
console.log(`📋 Platform: ${process.platform}`);
console.log(`📋 Architecture: ${process.arch}`);

// Check if required files exist
const requiredFiles = [
  'package.json',
  'quasar.config.ts',
  'src/App.vue'
];

console.log('🔍 Checking required files...');
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.error(`❌ ${file} is missing`);
    process.exit(1);
  }
}

// Check if node_modules exists
if (fs.existsSync('node_modules')) {
  console.log('✅ node_modules directory exists');
} else {
  console.log('⚠️  node_modules directory not found, running install...');
  try {
    execSync('yarn install --frozen-lockfile', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
}

// Run the build
console.log('🏗️  Starting Quasar build...');
try {
  // Force use of npx quasar to avoid any confusion
  execSync('npx quasar build', { stdio: 'inherit' });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.error('❌ Trying alternative build method...');
  try {
    execSync('yarn build', { stdio: 'inherit' });
    console.log('✅ Alternative build completed successfully');
  } catch (altError) {
    console.error('❌ Alternative build also failed:', altError.message);
    process.exit(1);
  }
}

// Verify build output
const distPath = path.join(__dirname, 'dist', 'spa');
if (fs.existsSync(distPath)) {
  const files = fs.readdirSync(distPath);
  console.log(`✅ Build output directory exists with ${files.length} files`);
  
  // Check for index.html
  if (files.includes('index.html')) {
    console.log('✅ index.html found in build output');
  } else {
    console.error('❌ index.html not found in build output');
    process.exit(1);
  }
} else {
  console.error('❌ Build output directory not found');
  process.exit(1);
}

console.log('🎉 Vercel build process completed successfully!');
